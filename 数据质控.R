### 经济负担调查问卷
setwd('E:\\OneDrive\\工作\\2025年度工作\\202502全国经济负担数据库构建\\数据质控方案')
# setwd('C:\\Users\\<USER>\\OneDrive\\工作\\2025年度工作\\202502全国经济负担数据库构建\\数据质控方案')
pacman::p_load( rio, plyr,dplyr,lubridate,tableone,tidyverse)
rm(list = ls()) 
 

# filename <- '经济负担问卷导出20250206110454581'
file.name <-  "经济负担问卷导出20250702151251315.xlsx"

# 疾病诊断 --------------------------------------------------------------------

dia.data <- import( '诊疗信息导出20250221134524.xlsx',which = 1)
dia.dictionary <- import( '诊疗信息导出20250221134524.xlsx',which = 2)


colnames(dia.data ) <- dia.dictionary$variables
data.var.list  <- dia.dictionary$variables

###修改列名 
var.merge.dia <- data.var.list [ grepl('-',data.var.list) & !grepl('-1',data.var.list)  ] # 提取题号（"-" 之前的部分）

df.merge.dia <- data.frame(column_name = var.merge.dia) %>%
  mutate(group = str_extract(var.merge.dia, "^[^\\-]+"))  # 提取"-"前的部分作为分组

# 按group分组 修改列名 
df_grouped.dia <- df.merge.dia %>% 
  group_by(group) %>%
  summarise(columns = list(column_name))  


### 包含按前缀分组的多选题 合并成一道题
for( i in 1:nrow(df_grouped.dia ) ){
  x <-  df_grouped.dia$group[i]
  df.dat2 <- dia.data  %>%
    select(  df_grouped.dia[  df_grouped.dia$group == x ,] [[2]][[1]] , d0.8 ) %>% 
    pivot_longer( cols =   df_grouped.dia[  df_grouped.dia$group == x ,] [[2]][[1]] , 
                  names_to = paste0(x,'.y' )  , 
                  values_to =  'value'  ) %>%
    filter(value ==1 ) %>%  # 只保留至少有 1 个值为 1，或者全是 NA 的行
    select(-value)  # 删除值列
  df.dat2 [,2] <-   apply( df.dat2[,2] ,1,function(x) {
    x <-  strsplit( x ,'-') 
    x <-  x[[1]][2]
  } ) %>% unlist() ## 删除多余
  
  dia.data <-  left_join( dia.data, df.dat2 , by= 'd0.8')
}
 
##  识别重复填写
name.duplicated <- dia.data [ duplicated( dia.data[, c('d0.6', 'd0.10' ) ] ), c('d0.6', 'd0.10' ) ]  

dia.data.dup <- 
  apply(  name.duplicated, 1, function(x){
    
    dat11 <- dia.data [ dia.data$d0.6== x[1] & dia.data$d0.10== x[2] , ]
    
    if ( all( dat11$d0.1 ==  1  )  ){## 全未填写
      
      dat11 <- dat11 %>%
        mutate( d0.14=  'all_blank'   ) 
      
    }else if (sum( dat11$d0.1 == 0 ) ==1  ) {
      
      dat11 <- dat11 %>%
        mutate( d0.14=  ifelse( dat11$d0.1 == 0,'keep','delete'  )) 
      
    }  else if ( sum( dat11$d0.1 == 0 ) >1    ) { ## 部分填写 或全填写
      # 计算每行的非缺失值数量（对所有列或指定列）
      dat11 <- dat11 %>%
        mutate( non_na_count = rowSums( !is.na( select(., `d1`:`d7-否` )  )))  
      max_non_na <- max(dat11$non_na_count)
      
      
      if(  sum( dat11$non_na_count == max_non_na  )==1 ){  #如果完整程度不一样 ，取最完整
        
        dat11 <- dat11 %>%
          mutate( d0.14 = ifelse( non_na_count == max_non_na ,  'keep' , 'delete' ) )   
        
      } else{  #如果完整程度不一样 ，取最新
        
        time11 <- max(ymd_hms(dat11$d0.11, tz = "Asia/Shanghai") )  
        
        dat11 <- dat11 %>%
          mutate( d0.14 = ifelse( non_na_count == max_non_na & d0.11 == time11 ,  'keep' , 'delete' ) )   
      }
      
    } 
    
  })  %>% ldply()

##把原始数据里重复记录填补为处理后的记录 
dia.data.deal <- dia.data
for(i in  dia.data.dup$.id  ){
  
  datt1 <- dia.data.dup[ dia.data.dup$.id==i,  ]
  
  if(any(datt1$d0.14=='keep')  ==1 ){
    # 找到非缺失值最多的行
    most_complete_row <- datt1 %>%
      filter(  d0.14== 'keep'  ) 
    
    dia.data.deal  <- dia.data.deal %>%
      mutate(across( c( `d1`:`d7-否` ,d6.y,d7.y ), ~ ifelse( d0.8 %in%  datt1$d0.8 ,  most_complete_row[[cur_column()]], .) )) 
    
  }
  
}


# 第二期问卷清洗--------------------------------------------------------------------
##  1.1 删除测试数据 
dat <-  import( file.name) %>% 
  filter(!grepl('2024-07-08|2024-07-10|2024-07-14', 随访日期 )) %>%
  filter( !grepl( '测试文案', 患儿姓名) )
dat2 <-  import(file.name, which = 2 ) %>% 
  filter(!grepl('2024-07-08|2024-07-10|2024-07-14|2024-08-17', 随访日期 )) %>% 
  filter( !grepl( '测试文案', 患儿姓名)) 

###  导入字典
dictionary1 <- import('经济负担字典.xlsx',which = 1 )
dictionary2 <- import('经济负担字典.xlsx',which = 2 )
# 换列名 
dat.var1.re <-colnames(dat) <- dictionary1$colname.full
dat.var2.re <- colnames(dat2) <- dictionary2$colname.full

####1.2  换列名 
### dat1  把同一变量不同选项，应合尽合
var.merge1 <- dat.var1.re [ grepl('-',dat.var1.re) & !grepl('-1',dat.var1.re)  ] # 提取题号（"-" 之前的部分）

df.merge1 <- data.frame(column_name = var.merge1) %>%
  mutate(group = str_extract(var.merge1, "^[^\\-]+"))  # 提取"-"前的部分作为分组

# 按 group 分组
df_grouped1 <- df.merge1 %>% 
  group_by(group) %>%
  summarise(columns = list(column_name))%>% 
  filter( group!="t4.13.1"   )   

for( i in 1:nrow(df_grouped1  ) ){
  
  x <-  df_grouped1$group[i]
  df.dat1 <- dat   %>%
    select(  df_grouped1[  df_grouped1$group == x ,] [[2]][[1]] , t5.8 )%>% 
    pivot_longer( cols =   df_grouped1[  df_grouped1$group == x ,] [[2]][[1]] , 
                  names_to = paste0(x,'.y' )  , 
                  values_to =  'value'  ) %>%
    filter(value ==1 ) %>%  # 只保留至少有 1 个值为 1，或者全是 NA 的行
    select(-value)  # 删除值列
  df.dat1 [,2] <-   apply( df.dat1[,2] ,1,function(x) {
    x <-  strsplit( x ,'-') 
    x <-  x[[1]][2]
  } ) %>% unlist() ## 删除多余
  
  dat  <-  left_join( dat , df.dat1 , by= 't5.8')
}

###dat2  把同一变量不同选项，应合尽合
var.merge2 <- dat.var2.re [ grepl('-',dat.var2.re) & !grepl('-1',dat.var2.re)  ] # 提取题号（"-" 之前的部分）

df.merge2 <- data.frame(column_name = var.merge2) %>%
  mutate(group = str_extract(var.merge2, "^[^\\-]+"))  # 提取"-"前的部分作为分组

# 按 group 分组
df_grouped2 <- df.merge2 %>% 
  group_by(group) %>%
  summarise(columns = list(column_name))%>% 
  filter( group!="t4.13.1"   )   

for( i in 1:nrow(df_grouped2  ) ){
  x <-  df_grouped2$group[i]
  df.dat2 <- dat2  %>%
    select(  df_grouped2[  df_grouped2$group == x ,] [[2]][[1]] , t5.8 ) %>% 
    pivot_longer( cols =   df_grouped2[  df_grouped2$group == x ,] [[2]][[1]] , 
                  names_to = paste0(x,'.y' )  , 
                  values_to =  'value'  ) %>%
    filter(value ==1 ) %>%  # 只保留至少有 1 个值为 1，或者全是 NA 的行
    select(-value)  # 删除值列
  df.dat2 [,2] <-   apply( df.dat2[,2] ,1,function(x) {
    x <-  strsplit( x ,'-') 
    x <-  x[[1]][2]
  } ) %>% unlist() ## 删除多余
  
  dat2 <-  left_join( dat2, df.dat2 , by= 't5.8')
}

### 1.3 去重
## 1.3.1 dat 识别重复记录
name.duplicated <-  dat [ duplicated( dat[, c('t0.1', 't5.1' ) ]) , c('t0.1', 't5.1' ) ]   #根据患儿姓名和随访员

###1.3.2 保留最完整的
delect.id <- 
  apply(  name.duplicated , 1, function(x){
    datt1 <-  dat [ dat$t0.1== x[1] & dat$t5.1== x[2] , ]
    
    # 计算每行的非缺失值数量（对所有列或指定列）
    datt1 <- datt1 %>%
      mutate( non_na_count = rowSums( !is.na(.)))
    # 找到非缺失值数量的最大值
    max_non_na <- max(datt1$non_na_count)
    min_non_na <- min(datt1$non_na_count)
    
    if(  max_non_na == min_non_na ){
      time11 <- max(ymd_hms(datt1$t5.2, tz = "Asia/Shanghai") )  
      
      result <- datt1 %>%
        filter(t5.2 == time11) %>%  #保留最新的
        select(t5.8 )  
    }else{   # 找到缺失值非最多的记录
      
      result <- datt1 %>%
        filter(non_na_count != max_non_na) %>%
        select(t5.8 )
    }
    
  }) %>% unlist   

dat  [ which(  dat $t5.8 %in% delect.id)  ,'t6.1' ] <- '重复填写'##标记

## 1.3.3 dat2 识别重复记录
name.duplicated2 <-   dat2 [ duplicated( dat2[, c('t0.1', 't5.1' ) ]) , c('t0.1', 't5.1' ) ] 

###1.3.4 保留最完整的
delect.id2 <- 
  apply(  name.duplicated2, 1, function(x){
    datt1 <-  dat2 [ dat2$t0.1== x[1] & dat2$t5.1== x[2] , ]
    # 计算每行的非缺失值数量（对所有列或指定列）
    datt1 <- datt1 %>%
      mutate( non_na_count = rowSums( !is.na(.)))
    # 找到非缺失值数量的最大值
    max_non_na <- max(datt1$non_na_count)
    min_non_na <- min(datt1$non_na_count)
    
    if(  max_non_na == min_non_na ){
      time11 <- max(ymd_hms(datt1$t5.2, tz = "Asia/Shanghai") )  
      result <- datt1 %>%
        filter(t5.2 == time11) %>%  #保留最新的
        select(t5.8 )  
    }else{   # 找到缺失值非最多的记录
      result <- datt1 %>%
        filter(non_na_count != max_non_na) %>%
        select(t5.8 )
    }
  }) %>% unlist 

##标记
dat2  [ which(  dat2 $t5.8 %in% delect.id2)  ,'t6.1' ] <- '重复填写'

###1.4  填写情况
dat['t6.1'] <- apply ( data.frame ( dat$t4.13.y,  dat$t6.1  ),1,function(x){
  if( !is.na( x[2] ) & x[2] == '重复填写'){
    '重复填写'
  }else if ( is.na( x[2]) & is.na(x[1] ) ){
    '部分填写'
  }else if ( is.na( x[2]) & !is.na(x[1] )  )
    '全部填写'
  
} ) 
table (dat ['t6.1'])

dat2['t6.1'] <- apply ( data.frame ( dat2$t4.13.y,  dat2$t6.1  ),1,function(x){
  if( !is.na( x[2] ) & x[2] == '重复填写'){
    '重复填写'
  }else if ( is.na( x[2]) & is.na(x[1] ) ){
    '部分填写'
  }else if ( is.na( x[2]) & !is.na(x[1] )  )
    '全部填写'
  
} ) 
table (dat2['t6.1'])


## 问卷连结诊疗信息
dat <- left_join(dat, dia.data.deal , by= c('t5.8'='d0.8' ))
dat2 <- left_join(dat2, dia.data.deal , by= c('t5.8'='d0.8' ))

##**** 删除重复记录
dat1.1 <- dat[ dat$t6.1!='重复填写' ,]    
dat2.1 <- dat2[ dat2$t6.1!='重复填写' , ]   

### 2 转变变量类型
col.name.dat1 <- c( 't3.1.2.1_1','t3.2_1','t3.3_1','t3.4','t3.4.1','t3.5.1.1' ,'t3.5.1.2',
           't3.5.2.1','t3.5.2.2','t3.5.3.1','t3.5.3.2','t3.5.4.1','t3.5.4.2','t3.5.5',
           't3.5.6','t3.5.7','t3.5.8','t3.6.1_1','t3.6.1.1',
           't3.6.1.1.1', 't3.6.1.1.2','t3.6.1.1.3','t3.6.1.1.4' ,
           't3.7.1.1.1', 't3.7.1.1.2','t3.7.1.1.3','t3.7.1.1.4', 
           't3.8', 't3.9','t3.10','t3.11','t3.12','t3.13','t3.14' ,
           't4.1','t4.2.1','t4.2.2','t4.3.1','t4.3.2','t4.4.1','t4.4.2',
           't4.4.1.1','t4.4.1.2','t4.4.2.1','t4.4.2.2',
           't4.5.1.1','t4.5.1.2','t4.5.2.1','t4.5.2.2',
           't4.6.1.1','t4.6.1.2','t4.6.2.1','t4.6.2.2') 

#检索汉字
charactor_data= dat1.1 %>%
  mutate(across( all_of(col.name.dat1 )  , as.character)) %>%  # 全部转字符
  select(t5.8,all_of(col.name.dat1 ) )    %>%
  pivot_longer(cols = -t5.8,
               names_to = "column",
               values_to = "value") %>%
  filter(grepl("[\u4e00-\u9fff]|O", value))
#检索汉字"不知道"为空
dat1.1 = dat1.1 %>% 
mutate(across(
  all_of (charactor_data$column),  ~ na_if( ., "不知道") ) ) 
 
dat1.1 [col.name.dat1] <- lapply(  dat1.1 [col.name.dat1 ]  , as.numeric)


col.name.dat2 <- c( 't3.1.2.1_1','t3.2_1','t3.3_1','t3.4','t3.4.1','t3.5.1.1' ,'t3.5.1.2',
          't3.5.2.1','t3.5.2.2','t3.5.3.1','t3.5.3.2','t3.5.4.1','t3.5.4.2','t3.5.5',
          't3.5.6','t3.5.7','t3.5.8','t3.6.1_1','t3.6.1.1' ,
          't3.6.1.1.1', 't3.6.1.1.2','t3.6.1.1.3','t3.6.1.1.4',
          't3.7.1.1.1', 't3.7.1.1.2','t3.7.1.1.3','t3.7.1.1.4',
          't3.8.1.1.1', 't3.8.1.1.2','t3.8.1.1.3','t3.8.1.1.4',
          't3.9','t3.10','t3.11','t3.12','t3.13','t3.14','t3.15',
          't4.1','t4.2.1','t4.2.2','t4.3.1','t4.3.2','t4.4.1','t4.4.2',
          't4.4.1.1','t4.4.1.2','t4.4.2.1','t4.4.2.2',
          't4.5.1.1','t4.5.1.2','t4.5.2.1','t4.5.2.2',
          't4.6.1.1','t4.6.1.2','t4.6.2.1','t4.6.2.2') 
 
charactor_data2= dat2.1 %>%
  mutate(across( all_of(col.name.dat2 )  , as.character)) %>%  # 全部转字符
  select(t5.8,all_of(col.name.dat2 ) )    %>%
  pivot_longer(cols = -t5.8,
               names_to = "column",
               values_to = "value") %>%
  filter(grepl("[\u4e00-\u9fff]|O", value))

print(charactor_data2,n=25)

dat2.1 = dat2.1 %>%
  mutate( 
    t3.4 = case_when(
      t3.4 == '七' ~ "7",  
      t3.4 == '50多万' ~ '50',
      t3.4 == '12万左右' ~ '12',
      TRUE ~ t3.4
    ),
    t3.3_1 = case_when(
      t3.3_1 == '10万' ~ '10',
      TRUE ~ t3.3_1
    ),
    t3.4.1 = case_when(
      t3.4.1 == '全部' ~ '10',
      t3.4.1 == '4万' ~ '4',
      TRUE ~ t3.4.1
    ),
    t3.5.1.1 = case_when(
      t3.5.1.1 == '10多万' ~ '10',
      t3.5.1.1 == '12万左右' ~ '12',
      TRUE ~ t3.5.1.1
    ),
    t3.5.1.2 = case_when(
      t3.5.1.2 == '10多万' ~ '10',
      t3.5.1.2 == '1 2万左右' ~ '12',
      TRUE ~ t3.5.1.2
    ),
    t3.5.2.1 = case_when(
      t3.5.2.1 == '40万左右' ~ '40',
      TRUE ~ t3.5.2.1
    ),
    t3.5.2.2 = case_when(
      t3.5.2.2 == '20多万' ~ '20',
      TRUE ~ t3.5.2.2
    ),
    t3.5.3.1 = case_when(
      t3.5.3.1 == '2万多' ~ '2',
      TRUE ~ t3.5.3.1
    ),
    t3.5.3.2 = case_when(
      t3.5.3.2 == '2万多' ~ '2',
      TRUE ~ t3.5.3.2
    ),
    t3.6.1.1 = case_when(
      t3.6.1.1 == '28万' ~ '28',
      TRUE ~ t3.6.1.1
    ),
    t3.6.1.1.1 = case_when(
      t3.6.1.1.1 == '百分之70-80' ~ '21',
      TRUE ~ t3.6.1.1.1
    ),
    t3.6.1.1.2 = case_when(
      t3.6.1.1.2 == '无' ~ NA_character_,
      TRUE ~ t3.6.1.1.2
    ),
    t3.6.1.1.3 = case_when(
      t3.6.1.1.3 == '无' ~ NA_character_,
      TRUE ~ t3.6.1.1.3
    ),
    t3.6.1.1.4 = case_when(
      t3.6.1.1.4 == '无' ~ NA_character_,
      TRUE ~ t3.6.1.1.4
    ),
    t4.6.1.1 = case_when(
      t4.6.1.1 == '无' ~ NA_character_,
      TRUE ~ t4.6.1.1
    ),
    t4.6.1.2 = case_when(
      t4.6.1.2 == '无' ~ NA_character_,
      TRUE ~ t4.6.1.2
    )
  )

dat2.1[col.name.dat2 ] <- lapply(dat2.1[col.name.dat2  ]  , as.numeric)

# 3 删除共享文档前质控不合格的记录
# 良性肿瘤保留？
# 009166 邓雁予，D27.x00(名称:卵巢良性肿瘤)  
# 006982 刘娜辰，D27.x00(名称:卵巢良性肿瘤)  
# 006895 李燕泽  D16.102(名称:掌骨良性肿瘤)
# 006504 孙川洋 D48.100x009(名称:软组织交界性肿瘤)
# 004632 董宜楠D47.900x001(名称:淋巴细胞增殖性疾病)
# 007391, #张雨萱 D47.900x001(名称:淋巴细胞增殖性疾病)

# 3.1 删除非肿瘤患儿
dat2.1 <- dat2.1 %>% filter(  !t5.8 %in% c('010570', # 杜学福  D69.406(名称:血小板减少性紫癜)
                                           '008859', #张籽橙 D76.200x001(名称:感染性噬血细胞综合征)
                                           '008782', #刘书豪D76.200x001(名称:感染性噬血细胞综合征)
                                           '008766', #邓万兴 D59.101(名称:自身免疫性溶血性贫血)
                                           '006033', #罗雅涵 D61.000x005(名称:布拉克凡-戴蒙德综合征[Blackfan-Diamong综合征])
                                           '005961', #王佳仪D61.000x005(名称:布拉克凡-戴蒙德综合征[Blackfan-Diamong综合征])
                                           '005958', #付婉晴D61.000x005(名称:布拉克凡-戴蒙德综合征[Blackfan-Diamong综合征])
                                           '005640', #李欣妍 非恶性肿瘤
                                           '005545'  #马天佑D61.000x005(名称:布拉克凡-戴蒙德综合征[Blackfan-Diamong综合征])                                       
) ) 
dat2.1 <- dat2.1 %>% filter(  !t5.8 %in% c('011071')) # 数据填写异常


###dat1.1 
##   门诊费用填错
## 赵晚柠## 住院费用填错
dat1.1$t3.5.2.1[ dat1.1$t5.8=='007060' ] <- dat1.1$t3.5.2.1[ dat1.1$t5.8=='007060' ]/10000
dat1.1$t3.5.2.2[ dat1.1$t5.8=='007060' ] <- dat1.1$t3.5.2.2[ dat1.1$t5.8=='007060' ]/10000


##张明琪## 药店填错
dat1.1$t3.5.3.1[ dat1.1$t5.8=='006734' ] <-  dat1.1$t3.5.3.1[ dat1.1$t5.8=='006734' ] /10000
dat1.1$t3.5.3.2[ dat1.1$t5.8=='006734' ] <-  dat1.1$t3.5.3.2[ dat1.1$t5.8=='006734' ]  /10000

## 许嘉亦##未分类填错
dat1.1$t3.5.4.1[ dat1.1$t5.8=='006945' ] <-  dat1.1$t3.5.4.1[ dat1.1$t5.8=='006945' ] /10000
dat1.1$t3.5.4.2[ dat1.1$t5.8=='006945' ] <-  dat1.1$t3.5.4.2[ dat1.1$t5.8=='006945' ]  /10000

##  高兆毅 个人支付 住院费用 填错  
dat1.1$t3.4[ dat1.1$t5.8=='006015' ] <-  dat1.1$t3.4[ dat1.1$t5.8=='006015' ]  /10000
 
dat1.1$t3.5.2.1[ dat1.1$t5.8=='006015' ] <-  dat1.1$t3.5.2.1[ dat1.1$t5.8=='006015' ]  /10000
dat1.1$t3.5.2.2[ dat1.1$t5.8=='006015' ] <-  dat1.1$t3.5.2.2[ dat1.1$t5.8=='006015' ]  /10000

dat1.1$t3.4[ dat1.1$t5.8 %in% c( '009821','009367','009720') ] <-  dat1.1$t3.4[ dat1.1$t5.8 %in% c( '009821','009367','009720')]  /10000


### dat2.1
#门诊
# 郑子媛 
dat2.1$t3.5.1.1[ dat2.1$t5.8=='007240' ] <-  dat2.1$t3.5.1.1[ dat2.1$t5.8=='007240' ] /10000
dat2.1$t3.5.1.2[ dat2.1$t5.8=='007240' ] <-  dat2.1$t3.5.1.2[ dat2.1$t5.8=='007240' ]  /10000
#钱泽凯
dat2.1$t3.5.1.1[ dat2.1$t5.8=='005976' ] <-  dat2.1$t3.5.1.1[ dat2.1$t5.8=='005976' ] /10000
dat2.1$t3.5.1.2[ dat2.1$t5.8=='005976' ] <-  dat2.1$t3.5.1.2[ dat2.1$t5.8=='005976' ]  /10000
##李博
dat2.1$t3.5.1.1[ dat2.1$t5.8=='006701' ] <-  dat2.1$t3.5.1.1[ dat2.1$t5.8=='006701' ] /10000
dat2.1$t3.5.1.2[ dat2.1$t5.8=='006701' ] <-  dat2.1$t3.5.1.2[ dat2.1$t5.8=='006701' ]  /10000
##何尘
dat2.1$t3.5.1.1[ dat2.1$t5.8=='005966' ] <-  dat2.1$t3.5.1.1[ dat2.1$t5.8=='005966' ] /10000
dat2.1$t3.5.1.2[ dat2.1$t5.8=='005966' ] <-  dat2.1$t3.5.1.2[ dat2.1$t5.8=='005966' ]  /10000
##周欣苒
dat2.1$t3.5.1.1[ dat2.1$t5.8=='005527' ] <-  dat2.1$t3.5.1.1[ dat2.1$t5.8=='005527' ] /10000
dat2.1$t3.5.1.2[ dat2.1$t5.8=='005527' ] <-  dat2.1$t3.5.1.2[ dat2.1$t5.8=='005527' ]  /10000

#于熙悦
dat2.1$t3.5.1.1[ dat2.1$t5.8=='004636' ] <-  dat2.1$t3.5.1.1[ dat2.1$t5.8=='004636' ] /10000
dat2.1$t3.5.1.2[ dat2.1$t5.8=='004636' ] <-  dat2.1$t3.5.1.2[ dat2.1$t5.8=='004636' ]  /10000

##陈文伊住院 门诊 个人支付
dat2.1$t3.5.1.1[ dat2.1$t5.8=='004608' ] <-  dat2.1$t3.5.1.1[ dat2.1$t5.8=='004608' ] /10000
dat2.1$t3.5.1.2[ dat2.1$t5.8=='004608' ] <-  dat2.1$t3.5.1.2[ dat2.1$t5.8=='004608' ]  /10000
dat2.1$t3.5.2.1[ dat2.1$t5.8=='004608' ] <-  dat2.1$t3.5.2.1[ dat2.1$t5.8=='004608' ] /10000
dat2.1$t3.5.2.2[ dat2.1$t5.8=='004608' ] <-  dat2.1$t3.5.2.2[ dat2.1$t5.8=='004608' ]  /10000
dat2.1$t3.4[ dat2.1$t5.8=='004608' ] <-  dat2.1$t3.4[ dat2.1$t5.8=='004608' ] /10000
 
dat2.1 <-  dat2.1 [ dat2.1$t5.8!='006130', ] ## 问卷随意乱填 

### 贫困救助 慈善救助  个人支付
dat2.1$t3.2_1[ dat2.1$t5.8=='005926' ] <-  dat2.1$t3.2_1[ dat2.1$t5.8=='005926' ] /10000
dat2.1$t3.3_1[ dat2.1$t5.8=='005926' ] <-  dat2.1$t3.2_1[ dat2.1$t5.8=='005926' ]  /10000
dat2.1$t3.4[ dat2.1$t5.8=='005926' ] <-  dat2.1$t3.4[ dat2.1$t5.8=='005926' ]  /10000 

# 将指定的费用变量从元转换为万元
# 只对t5.8=='008509'的观测进行单位转换（元转万元）
convert_specific_observations <- function(data) {
  
  print("开始对t5.8=='008509'的观测进行单位转换...")
  
  # 检查t5.8变量是否存在
  if(!"t5.8" %in% names(data)) {
    stop("数据中不存在t5.8变量")
  }
  
  # 找到需要转换的观测
  target_rows <- which(data$t5.8 == '008509')
  
  print(paste("找到", length(target_rows), "个t5.8=='008509'的观测"))
  
  if(length(target_rows) == 0) {
    print("没有找到t5.8=='008509'的观测，无需转换")
    return(data)
  }
  
  # 需要转换的变量列表
  vars_to_convert <- c(
    "t3.4", "t3.4.1",
    "t3.5.1.1", "t3.5.1.2", "t3.5.1_1",
    "t3.5.2.1", "t3.5.2.2", "t3.5.2_1", 
    "t3.5.3.1", "t3.5.3.2", "t3.5.3_1",
    "t3.5.4.1", "t3.5.4.2",
    "t3.5.5", "t3.5.6", "t3.5.7", "t3.5.8",
    "t3.6.1_1", "t3.6.1.1", "t3.6.1.1.1", "t3.6.1.1.2", "t3.6.1.1.3", "t3.6.1.1.4",
    "t3.7.1_1", "t3.7.1.1", "t3.7.1.1.1", "t3.7.1.1.2", "t3.7.1.1.3", "t3.7.1.1.4"
  )
  
  # 检查哪些变量存在于数据中
  existing_vars <- vars_to_convert[vars_to_convert %in% names(data)]
  
  print(paste("数据中存在的目标变量数量：", length(existing_vars)))
  
  # 转换摘要
  conversion_summary <- data.frame(
    变量名 = character(),
    转换的观测数 = numeric(),
    转换前示例值 = character(),
    转换后示例值 = character(),
    stringsAsFactors = FALSE
  )
  
  # 对每个存在的变量进行转换
  for(var in existing_vars) {
    # 找到目标行中该变量非空且大于0的记录
    target_data_indices <- target_rows[!is.na(data[target_rows, var]) & data[target_rows, var] > 0]
    
    if(length(target_data_indices) > 0) {
      # 记录转换前的示例值
      before_example <- data[target_data_indices[1], var]
      
      # 进行转换：只对目标观测的非空值除以10000
      data[target_data_indices, var] <- data[target_data_indices, var] / 10000
      
      # 记录转换后的示例值
      after_example <- data[target_data_indices[1], var]
      
      # 记录转换信息
      conversion_summary <- rbind(conversion_summary, data.frame(
        变量名 = var,
        转换的观测数 = length(target_data_indices),
        转换前示例值 = as.character(before_example),
        转换后示例值 = as.character(round(after_example, 4)),
        stringsAsFactors = FALSE
      ))
      
      cat("✓ 转换", var, ": ", length(target_data_indices), "个观测\n")
    } else {
      cat("- 跳过", var, ": 目标观测中无有效数据\n")
    }
  }
  
  # 显示转换摘要
  if(nrow(conversion_summary) > 0) {
    print("\n=== 转换摘要 ===")
    print(conversion_summary)
  }
  
  print(paste("\n✅ 完成转换，共处理", length(existing_vars), "个变量的", length(target_rows), "个观测"))
  
  return(data)
}

# 应用转换函数
if(exists("dat2.1")) {
  print("对dat2.1中t5.8=='008509'的观测进行单位转换...")
  
  # 转换前检查
  if("t5.8" %in% names(dat2.1)) {
    target_count <- sum(dat2.1$t5.8 == '008509', na.rm = TRUE)
    print(paste("转换前：找到", target_count, "个t5.8=='008509'的观测"))
    
    # 如果有目标观测，显示转换前的示例
    if(target_count > 0 && "t3.4" %in% names(dat2.1)) {
      target_idx <- which(dat2.1$t5.8 == '008509')[1]
      if(!is.na(dat2.1$t3.4[target_idx])) {
        print(paste("转换前t3.4示例值：", dat2.1$t3.4[target_idx]))
      }
    }
  }
  
  # 执行转换
  dat2.1 <- convert_specific_observations(dat2.1)
  
  # 转换后验证
  if("t5.8" %in% names(dat2.1) && "t3.4" %in% names(dat2.1)) {
    target_idx <- which(dat2.1$t5.8 == '008509')[1]
    if(length(target_idx) > 0 && !is.na(dat2.1$t3.4[target_idx])) {
      print(paste("转换后t3.4示例值：", dat2.1$t3.4[target_idx]))
    }
  }
  
} else {
  print("❌ dat2.1数据不存在，请先加载数据")
}
print("\n=== 特定观测单位转换完成 ===")
 
### 王竞 补充 监测平台信息
# 创建一个包含更新值的数据框
update_wangjing <- data.frame(
  t0.1 = "王竞",
  t1.1.y =  '男',
  t1.2='2007-08-12',
  t1.4.1 = '532627200708124119',
  t1.4.2 = '壮族',
  t2.2='C95.000(名称:急性白血病)',
  t1.3='云南省#文山壮族苗族自治州#广南县',
  t1.3.1='坝美镇堂上村委会',
  t1.5.y='门诊',
  t1.6.y='线上推送',
  t1.7.y='线上填写',
  t1.4.3='云南省#文山壮族苗族自治州#广南县',
  t1.4.3.1='坝美镇堂上村委会',
  t2.3= '2021-06-28',
  t5.3= '昆明市儿童医院',
  t6.1='部分填写',
  t3.5.1.1=5514.92,
  t3.5.1.2=1221.46,
  t3.5.2.1= 147946.17,
  t3.5.2.2=65467.9
)

# 更新原数据框
dat2.1 <- dat2.1 %>%
  rows_update(update_wangjing, by = "t0.1")

dat2.1[dat2.1$t0.1 == "王竞",]

# 问卷唯一识别码是 006062，患者杨文焕家属是患者哥哥，
# 配偶的信息部分无法填写，出生日期填了 2024 年 7 月，
# 收入影响填了毫无影响，配偶职业填了无业，需修改（清除内容或清除部分内容）
update_006062<- data.frame(
  t5.8 = "006062",
  t4.10='',
  t4.6.y='',
  t4.12.y=''
)
# 更新原数据框
dat2.1 <- dat2.1 %>%
  rows_update(update_006062, by = "t5.8")

 
## 检查dat2填写t3.9:t3.15元的单位是否填写成万元
dat2.1 %>% filter( t3.11< 100 &t3.11>0) %>%
 select( t5.8,t3.9:t3.15 )

## 只针对t3.9:t3.15所有都小于100的调整单位万元为元

dat2.1=  dat2.1 %>%
  mutate(
    cond = if_all(t3.9:t3.15, ~ is.na(.) | (. < 100 & . > 0)) & 
           if_any(t3.9:t3.15, ~ !is.na(.))
  ) %>%
  mutate(
    across(
      t3.9:t3.15,
      ~ ifelse(cond & !is.na(.) & . < 100 & . > 0, . * 10000, .)
    )
  ) %>%
  select(-cond)  
 
## 检查dat1填写t3.9:t3.14元的单位是否填写成万元
dat1.1 %>% filter( t3.11< 100 &t3.11>0) %>%
 select( t5.8,t3.9:t3.14 )
 
dat1.1=  dat1.1 %>%
  mutate(
    cond = if_all(t3.9:t3.14, ~ is.na(.) | (. < 100 & . > 0)) & 
           if_any(t3.9:t3.14, ~ !is.na(.))
  ) %>%
  mutate(
    across(
      t3.9:t3.14,
      ~ ifelse(cond & !is.na(.) & . < 100 & . > 0, . * 10000, .)
    )
  ) %>%
  select(-cond)  

 

# 第一期问卷 -------------------------------------------------------------------
# setwd("D:\\Desktop\\工作\\2025年度工作\\202502全国经济负担数据库构建\\数据质控方案")
# pacman::p_load( rio, plyr,dplyr,openxlsx,lubridate   )
# rm(list = ls())
###匹配医疗数据库
###匹配肿瘤登记
###导入数据
dat3 <-  import("经济负担问卷导出20250311181651118.xlsx", which =  1)
 
## 转换变量名
dictionary3 <- import( '经济负担字典.xlsx', which =  3)

colnames(dat3) <- dictionary3$variables
 
##添加唯一识别码 
dat3 <- dat3 %>%
  mutate(
  t0.7_system =  t0.7
  )
 
##添加唯一识别码 根据时间早晚 
dat3 <- dat3 %>%
  arrange( t0.5) %>%
  mutate( t0.7 =  paste0('2023',  sprintf('%04d', 1:4277)  ) )

###修改患儿姓名，小太阳---> 许龄兮
dat3$t0.1[dat3$t0.1=='小太阳'  ] <- '许龄兮'

# ###调整串行问题
# # 赵博妍 数据串行
change.id1 <-  dat3$t0.7[ dat3$t0.1== '赵博妍']
# 蒋宇宸 数据串行
change.id2 <-dat3$t0.7[ dat3$t0.1== '蒋宇宸']
# 删除串行
col_names <- names(dat3[ ,which( names(dat3) == 't4.5.1' ):which( names(dat3) =='t5.11') ])
col_names1 <- names(dat3[ ,which( names(dat3) == 't5.4_2' ):which( names(dat3) =='t5.18') ])

# 将第 6 列及以后的列向左移动 5 列
 for (i in 1:(length(col_names))) {
   dat3 [ col_names[i]] [ dat3$t0.7 ==  c(change.id1),] <- dat3 [ col_names1[i] ] [ dat3$t0.7 ==  c(change.id1),]
   dat3 [ col_names[i]] [ dat3$t0.7 ==  c(change.id2),] <- dat3 [ col_names1[i] ] [ dat3$t0.7 ==  c(change.id2),]
 }
 
update_20234276<- data.frame(
  t0.7='20234276',
  t0.4='徐棱霄',
  t0.5='2023-07-28 11:21:06',
  t0.6 = "首都儿科研究所附属儿童医院" ,
  t0.7_system='000236'
)
update_20234277<- data.frame(
  t0.7='20234277',
  t0.4='吴进军',
  t0.5='2023-07-28 10:26:07',
  t0.6 = "武汉儿童医院" ,
  t0.7_system='000194'
)


# 更新原数据框
dat3 <- dat3 %>%
  rows_update(update_20234276, by = "t0.7")
# 更新原数据框
dat3 <- dat3 %>%
  rows_update(update_20234277, by = "t0.7")
  
##删除测试数据
##重复数据标记
name.duplicated3 <-   dat3 [ duplicated( dat3[, c('t0.1', 't0.4' ) ]) , c('t0.1', 't0.4' ) ] 

###保留最完整的
delect.id3 <- 
  apply(  name.duplicated3, 1, function(x){
    datt1 <-  dat3 [ dat3$t0.1== x[1] & dat3$t0.4== x[2] , ]
    # 计算每行的非缺失值数量（对所有列或指定列）
    datt1 <- datt1 %>%
      mutate( non_na_count = rowSums( !is.na(.)))
    # 找到非缺失值数量的最大值
    max_non_na <- max(datt1$non_na_count)
    min_non_na <- min(datt1$non_na_count)
    
    if(  max_non_na == min_non_na ){
      time11 <- max(ymd_hms(datt1$t0.5, tz = "Asia/Shanghai") )  
      result <- datt1 %>%
        filter(t0.5 == time11) %>%  #保留最新的
        select(t0.7 )  
    }else{   # 找到缺失值非最多的记录
      result <- datt1 %>%
        filter(non_na_count != max_non_na) %>%
        select(t0.7 )
    }
  }) %>% unlist 

##标记
dat3  [ which(  dat3$t0.7 %in% delect.id3)  ,'t0.8' ] <- '重复填写'

### 填写情况
dat3['t0.8'] <- apply ( data.frame ( dat3$t5.11,  dat3$t0.8  ),1,function(x){
  if( !is.na( x[2] ) & x[2] == '重复填写'){
    '重复填写'
  }else if ( is.na( x[2]) & is.na(x[1] ) ){
    '部分填写'
  }else if ( is.na( x[2]) & !is.na(x[1] )  )
    '全部填写'
  
} ) 
table (dat3 ['t0.8'])

## 删除重复记录 
dat3.1 <- dat3[ dat3$t0.8!='重复填写' ,]    


# 如有修改，则线上数据均会保留，修改跳转题的逻辑
## t2.4
dat3.1$t2.4 [dat3.1$t2.4.1=='5326201405302115' ] <-  1
dat3.1$t2.4.1 [dat3.1$t2.4.1=='无' ] <-  NA

##t2.10.3
dat3.1 <-dat3.1 %>%
  mutate( t2.10 = ifelse(
    rowSums( !is.na( dat3.1[ which(names(dat3.1)=='t2.10.1'):which(names(dat3.1)=='t2.10.3_4_1')] )  >0 ) ,
    1, t2.10 ) )
 
# t4.1_1:t4.9_1
colname.t4.1 <-  names(dat3.1)  [grepl( 't4.1', names(dat3.1) ) &!grepl( '_[1-9]_' , names(dat3.1) )  ]
                                   
dat3.1 <-dat3.1 %>%
  mutate( across( all_of(colname.t4.1 )  ,~ {
    current_data <- cur_data()
    current_col_index <- which(names(current_data) ==cur_column() )
    target_col_index <- current_col_index + 1
    
    # 检查目标列是否存在
    if (target_col_index > ncol(current_data)) {
      return(.)
    }
    
    ifelse(!is.na(current_data[[target_col_index]][row_number()]) & 
             !current_data[[target_col_index]] [row_number()]%in% c('0','0次') , 1, .)
    
  }
  ))

# t5.4_1
dat3.1 <- dat3.1 %>%
  mutate(t5.4_1 =  ifelse( 
    rowSums( dat3.1[which(names(dat3.1)=='t5.4_2'):which( names(dat3.1)=='t5.4_5') ]==1 )>=1,
    0,t5.4_1
    ) )
  
 
# t5.4_2_1:t5.4_5_1
#如果t5.4_2:t5.4_5 是0，相应的跳转清空
comt5 <- expand.grid(c(4,9), 2:4)
colname.t5.4_1_1 <- paste0( 't5.',comt5$Var1,"_",comt5$Var2,'_1')  

dat3.1<-dat3.1 %>%
  mutate( across( all_of(colname.t5.4_1_1 )  ,~ {
    current_data <- cur_data()
    current_col_index <- which(names(current_data) ==cur_column() )
    target_col_index  <- current_col_index - 6
 
    # 检查目标列是否存在
    if (target_col_index  > ncol(current_data)) {
      return(.)
    }
    
    ifelse( current_data[[target_col_index]][row_number()]==0 , NA, .)

  }
  ))

# t5.4_2_1_1:t5.4_5_1_1
colname.t5.4_1_1_1 <- paste0( 't5.',comt5$Var1,"_",comt5$Var2,'_1_1')  

dat3.1<-dat3.1 %>%
  mutate( across( all_of(colname.t5.4_1_1_1 )  ,~ {
    current_data <- cur_data()
    current_col_index <- which(names(current_data) ==cur_column() )
    target_col_index  <- current_col_index - 7
    
    # 检查目标列是否存在
    if (target_col_index  > ncol(current_data)) {
      return(.)
    }
    
    ifelse( current_data[[target_col_index]][row_number()]==0 , NA, .)
    
  }
  ))

 

dat3.1$t2.10.3_1_1[grepl("[\u4e00-\u9fff]", dat3.1 $t2.10.3_1_1)]
dat3.1 <- dat3.1 %>% 
  mutate( 
    t2.10.3_1_1= case_when(
      t2.10.3_1_1== "一个月" ~ '1', 
      t2.10.3_1_1== "4个1" ~ '4',  
      t2.10.3_1_1== "2年" ~ '24', 
      t2.10.3_1_1== "1125天" ~  '37.5',
      TRUE ~ t2.10.3_1_1
    )
  )

dat3.1$t2.10.3_2_1[grepl("[\u4e00-\u9fff]", dat3.1 $t2.10.3_2_1)]

dat3.1 <- dat3.1 %>% 
  mutate( 
    t2.10.3_2_1= case_when(
      t2.10.3_2_1== "1年" ~ '12', 
      t2.10.3_2_1== "3个月" ~ '3',  
      t2.10.3_2_1== "一年" ~ '12', 
      t2.10.3_2_1== "2年" ~  '24',
      t2.10.3_2_1== "2年半" ~  '30',
      t2.10.3_2_1== "刚确诊，还没申请休学" ~  NA_character_,
      t2.10.3_2_1== "否" ~  NA_character_,
      t2.10.3_2_1== "6月" ~  '6',
      t2.10.3_2_1== "二年多时间" ~  '24',
      t2.10.3_2_1== "2个月" ~  '2',
      t2.10.3_2_1== "半年" ~  '6',
      t2.10.3_2_1== "确诊后一直没有上学，6个月没有上学" ~  '6',
      TRUE ~ t2.10.3_2_1
    )
  )


dat3.1$t2.10.3_3_1[grepl("[\u4e00-\u9fff]", dat3.1 $t2.10.3_3_1)]

dat3.1 <- dat3.1 %>% 
  mutate( 
    t2.10.3_3_1= case_when(
      t2.10.3_3_1== "1年" ~ '12', 
      t2.10.3_3_1== "3个月" ~ '3',  
      TRUE ~ t2.10.3_3_1
    )
  )

dat3.1$t2.10.3_4_1[grepl("[\u4e00-\u9fff]", dat3.1 $t2.10.3_4_1)]
dat3.1$t2.10.3_4_1 <-as.character(dat3.1$t2.10.3_4_1 ) 

#### t3.1_1
dat3.1$t3.1_1[grepl("[\u4e00-\u9fff]", dat3.1$t3.1_1)]

dat3.1 <- dat3.1 %>% 
  mutate( 
    t3.1_1= case_when(
      t3.1_1== "4万5" ~ '4.5', 
      t3.1_1== "12万" ~ '12',  
      t3.1_1== "7万5" ~ '7.5',  
      t3.1_1== "8万元" ~ '8',  
      t3.1_1== "2万7" ~ '2.7',  
      TRUE ~ t3.1_1
    )
  )
#t3.2_1
dat3.1$t3.2_1[grepl("[\u4e00-\u9fff]", dat3.1$t3.2_1)]

dat3.1 <- dat3.1 %>% 
  mutate( 
    t3.2_1= case_when(
      t3.2_1== "7万" ~ '7', 
      t3.2_1== "3万5" ~ '3.5',  
      t3.2_1== "200000元" ~ '20',  
      t3.2_1== "9万元" ~ '9',  
      TRUE ~ t3.2_1
    )
  )
#t3.2.1_1
dat3.1$t3.2.1_1[grepl("[\u4e00-\u9fff]", dat3.1$t3.2.1_1)]

dat3.1 <- dat3.1 %>% 
  mutate( 
    t3.2.1_1= case_when(
      t3.2.1_1== "5千" ~ '0.5', 
      t3.2.1_1== "1万2" ~ '1.2',  
      t3.2.1_1== "20000元" ~ '2',  
      t3.2.1_1== "2万元" ~ '2',  
      TRUE ~ t3.2.1_1
    )
  )

dat3.1$t3.2.2_1[grepl("[\u4e00-\u9fff]", dat3.1$t3.2.2_1)]

dat3.1 <- dat3.1 %>% 
  mutate( 
    t3.2.2_1= case_when(
      t3.2.2_1== "5万" ~ '5', 
      t3.2.2_1== "2万3" ~ '2.3',  
      t3.2.2_1== "20000元" ~ '2',  
      t3.2.2_1== "6万元" ~ '6',  
      TRUE ~ t3.2.2_1
    )
  )
  
dat3.1$t4.4[dat3.1$t0.7_system== '002149' ]   <- NA

## t4.4.1_1_3:"t4.4.1_13_6"
#检索汉字
 dat3.1 %>%
  mutate(across( paste0( 't4.4.1_', expand.grid(1:13, 3:6)$Var1 , '_', expand.grid(1:13, 3:6)$Var2)    , as.character)) %>%  # 全部转字符
  select(t0.7_system, paste0( 't4.4.1_', expand.grid(1:13, 3:6)$Var1 , '_', expand.grid(1:13, 3:6)$Var2))    %>%
  pivot_longer(cols = -t0.7_system,
               names_to = "column",
               values_to = "value") %>%
  filter(grepl("[\u4e00-\u9fff]|O", value))

 dat3.1 <- dat3.1 %>% 
   mutate( 
     t4.4.1_1_3= case_when(
       t4.4.1_1_3== "O" ~ NA_character_, 
       TRUE ~ t4.4.1_1_3
     ),
     t4.4.1_1_4= case_when(
       t4.4.1_1_4== "高铁" ~ NA_character_, 
       t4.4.1_1_4== "开车" ~ NA_character_,  
       t4.4.1_1_4== "7万元" ~ '7',  
       t4.4.1_1_4== "2万" ~ '7',  
       TRUE ~ t4.4.1_1_4
     ),
     t4.4.1_1_5= case_when(
       t4.4.1_1_5== "35万" ~ '35', 
       t4.4.1_1_5== "5千" ~ '0.5', 
       TRUE ~ t4.4.1_1_5
     ),
     t4.4.1_1_6= case_when(
       t4.4.1_1_6== "1万" ~ '1', 
       TRUE ~ t4.4.1_1_6
     )
   )
 
# 7月29号及以后  化疗、放疗、靶向治疗次数改为疗程   怎么定义疗程?
 
 dat3.1 %>%
   mutate(across(all_of(   paste0 ('t4.1_',  1:8, '_1') ), as.character)) %>%  # 全部转字符
   select(t0.7_system, all_of(   paste0 ('t4.1_',  1:8, '_1') )) %>%
   pivot_longer(
     cols = -t0.7_system,
     names_to = "column",
     values_to = "value"
   ) %>%
   filter(!is.na(value) & grepl("[\u4e00-\u9fff]", value))   # 匹配汉字
 
 dat3.1 <- dat3.1 %>% 
   mutate( 
     t4.1_1_1= case_when(
       t4.1_1_1=='0次'  ~ NA_character_,  
       TRUE ~ t4.1_1_1
     ),
     t4.1_7_1= case_when(
       t4.1_7_1=='止疼'  ~ NA_character_,  
       TRUE ~ t4.1_7_1
     ),
     t4.1_3_1= case_when(
       t4.1_3_1=='输液'~ NA_character_,  
       t4.1_3_1=='一' ~ "1疗程",
       t4.1_3_1=='第一疗程' ~ "1疗程",
       TRUE ~ t4.1_3_1
     )
       
     )
 # 转日期
 dat3.1 <- dat3.1 %>%
   mutate(t0.5_date = as.Date(t0.5, "%Y-%m-%d %H:%M:%S"))
 
 # 补充 t4.1_1_1 ~ t4.1_3_1
 cols_1_3 <- paste0('t4.1_', 1:3, '_1')
 
 dat3.1 <- dat3.1 %>%
   mutate(across(all_of(cols_1_3), as.character)) %>%
   mutate(across(all_of(cols_1_3),
                 ~ case_when(
                   !is.na(.) & t0.5_date >= as.Date("2023-07-29") & !grepl("疗程$", .) ~ paste0(., "疗程"),
                   !is.na(.) & t0.5_date < as.Date("2023-07-29") & !grepl("次$", .) ~ paste0(., "次"),
                   TRUE ~ .
                 )))
 
 # 补充 t4.1_4_1 ~ t4.1_8_1
 cols_4_8 <- paste0('t4.1_', 4:8, '_1')
 dat3.1 <- dat3.1 %>%
   mutate(across(all_of(cols_4_8), as.character)) %>%
   mutate(across(all_of(cols_4_8),
                 ~ ifelse(!is.na(.) & . != "", paste0(., "次"), .)))
 
  
 
# ## 转变数据类型
  
cols_need_numeric <- c( paste0 ('t2.10.3_',  1:4,  '_1'   ),
   't3.1_1', 't3.2_1',  't3.2.1_1', 't3.2.2_1',
   't4.2', 't4.2.1', 't4.2.3', 't4.3', 't4.3.1','t4.3.3',
   't4.4',
   paste0( 't4.4.1_', expand.grid(1:13, 3:6)$Var1 , '_', expand.grid(1:13, 3:6)$Var2) ,
   paste0( 't4.5.',1:9 ), 't4.5.10_1',
   't5.1','t5.2',
   't5.4_2_1', 't5.4_2_1_1', 't5.4_3_1', 't5.4_3_1_1',  't5.4_4_1', 't5.4_4_1_1',
   't5.5.1_2', 't5.5.1_3',
   't5.6','t5.7',
   't5.9_2_1', 't5.9_2_1_1', 't5.9_3_1', 't5.9_3_1_1','t5.9_4_1', 't5.9_4_1_1',
   't5.10.1_2', 't5.10.1_3',
   't5.11.1' )

dat3.1[ cols_need_numeric] <- lapply( dat3.1[ cols_need_numeric ], as.numeric)

 


#问卷变更
# 1、7月14日更新，影响范围：预调研7月11日和7月12日
# 预调研的问卷：直接医疗与非医疗费用部分：5、请填写自患儿确诊至今，为了治疗肿瘤，除了付给医院的钱之外，其它方面的累计花费下子题目，单位由万元修改为元
## 标记 预调查数据， 后期剔除
dat3.1  <- dat3.1 %>% 
  mutate(across( c(t4.5.1:t4.5.9,t4.5.10_1)  , ~  ifelse( grepl( '2023-07-1[1|2]', dat3.1$t0.5 ) , dat3.1[[cur_column()]]*10000 ,. ) ) ) 
    

# 7月25日更新职业字典，7月11日和7月12日预调研的问卷已选内容后台数据已处理
# 7月28号开展全国调研，中午召开修改问卷会议  
# 7月29号及以后  化疗、放疗、靶向治疗次数改为疗程   怎么定义疗程?  已处理
# 7月11日和7月12日7月28号 门诊、住院单次费用， 数据无法确定是报销前还是报销后  ???   不处理 当敏感性分析
# 7月29号以后的数据  门诊、住院根据实际情况，填写报销前或报销后的总费用
# 7月30号 跨省就医修改为 类型、总次数、住宿费、餐饮费、交通费  跨省就医题 目调整，4）、5）、6）由本次跨省就医花费修改为此类跨省就医总花费（可将默认新增的跨省就医次数为1即可，无需处理）
###所有跳转题都要注意 更改主题干后，跳转题内容有保留
 
### 检查所有单位是元的 有没有填写成万元。。。
# 1） 系统性填写错误  都是0.几
#2）  三个问卷都检查

 # paste0( 't4.4.1_', expand.grid(1:13, 4:6)$Var1 , '_', expand.grid(1:13, 4:6)$Var2)
  dat3.1 %>%
  filter(if_any(paste0( 't4.4.1_', expand.grid(1:13, 4:6)$Var1 , '_', expand.grid(1:13, 4:6)$Var2) , ~ . < 100 & .>0 & !is.na(.)    )) %>%
  # mutate(across(paste0( 't4.4.1_', expand.grid(1:13, 4:6)$Var1 , '_', expand.grid(1:13, 4:6)$Var2) , ~ . * 10000)) %>%
  select(t0.7, paste0( 't4.4.1_', expand.grid(1:13, 4:6)$Var1 , '_', expand.grid(1:13, 4:6)$Var2) )

  dat3.1 %>%
  filter(if_any(paste0( 't4.4.1_', expand.grid(1:13, 4:6)$Var1 , '_', expand.grid(1:13, 4:6)$Var2) , ~ . < 100 & .>0 & !is.na(.)    )) %>%
  # mutate(across(paste0( 't4.4.1_', expand.grid(1:13, 4:6)$Var1 , '_', expand.grid(1:13, 4:6)$Var2) , ~ . * 10000)) %>%
  select(t0.7, paste0( 't4.4.1_', expand.grid(1:13, 4:6)$Var1 , '_', expand.grid(1:13, 4:6)$Var2) )
## 修改
# t4.4.1 部分
dat3.1 = dat3.1 %>%
  mutate(
    cond = if_all(
      all_of(paste0("t4.4.1_", expand.grid(1:13, 4:6)$Var1, "_", expand.grid(1:13, 4:6)$Var2)),
      ~ is.na(.) | (. > 0 & . < 100)
    ) &
    if_any(
      all_of(paste0("t4.4.1_", expand.grid(1:13, 4:6)$Var1, "_", expand.grid(1:13, 4:6)$Var2)),
      ~ !is.na(.)
    )
  ) %>%
  mutate(
    across(
      all_of(paste0("t4.4.1_", expand.grid(1:13, 4:6)$Var1, "_", expand.grid(1:13, 4:6)$Var2)),
      ~ ifelse(cond & !is.na(.) & . > 0 & . < 100, . * 10000, .)
    )
  ) %>%
  select(-cond)

# t4.5.1:9 部分
dat3.1 = dat3.1 %>%
  mutate(
    cond = if_all(
      all_of(paste0("t4.5.", 1:9)),
      ~ is.na(.) | (. > 0 & . < 100)
    ) &
    if_any(
      all_of(paste0("t4.5.", 1:9)),
      ~ !is.na(.)
    )
  ) %>%
  mutate(
    across(
      all_of(paste0("t4.5.", 1:9)),
      ~ ifelse(cond & !is.na(.) & . > 0 & . < 100, . * 10000, .)
    )
  ) %>%
  select(-cond)
 

# 匹配疾病诊断 ------------------------------------------------------------------

diagnosis_id <- import('z_var2.sas7bdat')
diagnosis_id <- diagnosis_id %>%  filter( var2 !="")
diagnosis_id <- diagnosis_id[ !duplicated(diagnosis_id)  ,] 

diagnosis_id.dup <-  diagnosis_id [diagnosis_id$var2 %in%  diagnosis_id$var2[duplicated(diagnosis_id$var2)  ], ]  %>% arrange(var2)
###多源发
diagnosis_id.dup1 <- diagnosis_id.dup %>%
  group_by(var2, ICCC3_LARGE_GROUP) %>%      # 按两列分组
  filter (n() > 1) %>%                       # 保留出现次数>1的组
  ungroup() %>%                              # 解除分组
  arrange(var2)                              # 按var2排序
##只保留 填写更全的
diagnosis_id.dup1.1 <- diagnosis_id.dup1 %>%
mutate(non_na_count = rowSums(. != "" & !is.na(.))) %>%  # 计算每行非NA数量
  group_by(var2, ICCC3_LARGE_GROUP) %>%    # 再次分组
  filter(non_na_count == max(non_na_count)) %>%  # 保留信息量最多的
  slice(1) %>%                             # 如果有并列，保留第一条
  ungroup() %>%                            # 解除分组
  arrange(var2)                            # 排序
###多源发
diagnosis_id.dup2 <- diagnosis_id.dup %>%
  group_by(var2, ICCC3_LARGE_GROUP) %>%      # 按两列分组
  filter (n() <= 1) %>%                       # 保留出现次数>1的组
  ungroup() %>%                              # 解除分组
  arrange(var2)                              # 按var2排序


diagnosis_id.nondup <- diagnosis_id[!diagnosis_id$var2 %in% c(diagnosis_id.dup1$var2,diagnosis_id.dup2$var2 ),]

diagnosis_id.nondup <- diagnosis_id.nondup %>% 
  bind_rows(diagnosis_id.dup1.1 ) %>% 
  select(-non_na_count)
##匹配诊断
dat1.1.1 <- left_join(dat1.1, diagnosis_id.nondup, by= c('t5.9'='var2'))
dat2.1.1 <- left_join(dat2.1, diagnosis_id.nondup, by= c('t5.9'='var2'))
dat3.1.1 <- left_join(dat3.1, diagnosis_id.nondup, by= c('main_id'='var2'))

### 通过诊断调查填补iccc3
  
# 只对ICCC3_LARGE_GROUP为空且d2有内容的行进行修改
dat1.1.1 <- dat1.1.1 %>%
  mutate( ICCC3_LARGE_GROUP= gsub( 'X', 'Ⅹ',  ICCC3_LARGE_GROUP) ,
          ICCC3_LARGE_GROUP= gsub( 'V', 'Ⅴ',   ICCC3_LARGE_GROUP) ,
          ICCC3_SMALL_GROUP= gsub( 'V', 'Ⅴ',  ICCC3_SMALL_GROUP) ,
          ICCC3_SMALL_GROUP= gsub( 'X', 'Ⅹ',   ICCC3_SMALL_GROUP) ,
          ICCC3_SITE_GROUP= gsub( 'V', 'Ⅴ',   ICCC3_SITE_GROUP) ,
          ICCC3_SITE_GROUP= gsub( 'X', 'Ⅹ',   ICCC3_SITE_GROUP)  ,
          d2 = gsub( 'X', 'Ⅹ',   d2)  , 
          d2 = gsub( 'V', 'Ⅴ',    d2)  , 
  ) %>%

  # 只对ICCC3_LARGE_GROUP为空且d2有内容的行进行修改
  mutate(
    # 提取括号中的编号
    iccc_code = ifelse(
      ICCC3_LARGE_GROUP == "" & !is.na(d2),
      str_extract(d2, "\\(([^)]+)\\)") %>% str_remove_all("[()]"),
      NA
    ),
    
    # ICCC3_LARGE_GROUP：只要有iccc_code就填入罗马数字部分
    ICCC3_LARGE_GROUP = case_when(
      !is.na(iccc_code) ~ str_extract(iccc_code, "^[ⅠⅡⅢⅣⅤⅥⅦⅩⅫⅨⅧ]+"),
      TRUE ~ ICCC3_LARGE_GROUP  # 其他行保持原值
    ) 

  ) %>%
  mutate(
    # ICCC3_SMALL_GROUP：只要有iccc_code就填入罗马数字+字母部分
    ICCC3_SMALL_GROUP = case_when(
      !is.na(iccc_code) & str_detect(iccc_code, "[A-Za-z]") ~ str_extract(iccc_code, "^[ⅠⅡⅢⅣⅤⅥⅦⅩⅫⅨⅧ]+\\.[A-Za-z]*"),
      TRUE ~ ICCC3_SMALL_GROUP  # 其他行保持原值
    )
  ) %>%
  mutate(
    # ICCC3_SITE_GROUP：只要有iccc_code就填入完整编号（罗马数字+字母+数字）
    ICCC3_SITE_GROUP = case_when(
      !is.na(iccc_code) &  str_detect(iccc_code, "\\d") ~ iccc_code,
      TRUE ~ ICCC3_SITE_GROUP  # 其他行保持原值
    )
  )
  
dat2.1.1 <- dat2.1.1 %>%
  mutate( ICCC3_LARGE_GROUP= gsub( 'X', 'Ⅹ',  ICCC3_LARGE_GROUP) ,
          ICCC3_LARGE_GROUP= gsub( 'V', 'Ⅴ',   ICCC3_LARGE_GROUP) ,
          ICCC3_SMALL_GROUP= gsub( 'V', 'Ⅴ',  ICCC3_SMALL_GROUP) ,
          ICCC3_SMALL_GROUP= gsub( 'X', 'Ⅹ',   ICCC3_SMALL_GROUP) ,
          ICCC3_SITE_GROUP= gsub( 'V', 'Ⅴ',   ICCC3_SITE_GROUP) ,
          ICCC3_SITE_GROUP= gsub( 'X', 'Ⅹ',   ICCC3_SITE_GROUP)  ,
          d2 = gsub( 'X', 'Ⅹ',   d2)  , 
          d2 = gsub( 'V', 'Ⅴ',    d2)  , 
  ) %>%
  
  # 只对ICCC3_LARGE_GROUP为空且d2有内容的行进行修改
  mutate(
    # 提取括号中的编号
    iccc_code = ifelse(
      ICCC3_LARGE_GROUP == "" & !is.na(d2),
      str_extract(d2, "\\(([^)]+)\\)") %>% str_remove_all("[()]"),
      NA
    ),
    
    # ICCC3_LARGE_GROUP：只要有iccc_code就填入罗马数字部分
    ICCC3_LARGE_GROUP = case_when(
      !is.na(iccc_code) ~ str_extract(iccc_code, "^[ⅠⅡⅢⅣⅤⅥⅦⅩⅫⅨⅧ]+"),
      TRUE ~ ICCC3_LARGE_GROUP  # 其他行保持原值
    ) 
    
  ) %>%
  mutate(
    # ICCC3_SMALL_GROUP：只要有iccc_code就填入罗马数字+字母部分
    ICCC3_SMALL_GROUP = case_when(
      !is.na(iccc_code) & str_detect(iccc_code, "[A-Za-z]") ~ str_extract(iccc_code, "^[ⅠⅡⅢⅣⅤⅥⅦⅩⅫⅨⅧ]+\\.[A-Za-z]*"),
      TRUE ~ ICCC3_SMALL_GROUP  # 其他行保持原值
    )
  ) %>%
  mutate(
    # ICCC3_SITE_GROUP：只要有iccc_code就填入完整编号（罗马数字+字母+数字）
    ICCC3_SITE_GROUP = case_when(
      !is.na(iccc_code) &  str_detect(iccc_code, "\\d") ~ iccc_code,
      TRUE ~ ICCC3_SITE_GROUP  # 其他行保持原值
    )
  )


# 第一期问卷 -------------------------------------------------------------------

#t3.1_1 : t4.3.3  元改成万元单位

dat3.1.1 <- dat3.1.1 %>%
  mutate(
    t3.1_1 = case_when( 
      t3.1_1 >=1000 ~ t3.1_1 /10000,
      TRUE ~ t3.1_1
    ),
    t3.2_1 = case_when( 
      t3.2_1 >=1000 ~ t3.2_1 /10000,
      TRUE ~ t3.2_1
    ), 
    t3.2.1_1 = case_when( 
      t3.2.1_1 >=1000 ~ t3.2.1_1 /10000,
      TRUE ~ t3.2.1_1
    ), 
    t4.2.1 = case_when( 
      t4.2.1 >=1000 ~ t4.2.1 /10000,
      TRUE ~ t4.2.1
    ),
    t4.2.3 = case_when( 
      t4.2.3 >=1000 ~ t4.2.3 /10000,
      TRUE ~ t4.2.3
    ),
    t4.2.0 = case_when( 
      t4.2.0 >=1000 ~ t4.2.0 /10000,
      TRUE ~ t4.2.0
    ),
    t4.3.0 = case_when( 
      t4.3.0 >=1000 ~ t4.3.0 /10000,
      TRUE ~ t4.3.0
    ),
    t4.3.1 = case_when( 
      t4.3.1 >=1000 ~ t4.3.1 /10000,
      TRUE ~ t4.3.1
    ),
    t4.3.3 = case_when( 
      t4.3.3 >=1000 ~ t4.3.3 /10000,
      TRUE ~ t4.3.3
    ) )
 
###  把9999置空 
dat3.1.1 <- dat3.1.1 %>%
  mutate(
    t5.1  = case_when( 
      t5.1 %in% c( 9999, 999 , 99 )   ~ NA,
      TRUE ~ t5.1
    ),
    t5.2  = case_when( 
      t5.2%in% c( 9999, 999 , 99)  ~ NA,
      TRUE ~ t5.2
    ),
    t5.6 = case_when( 
      t5.6%in% c( 9999, 999, 99 )  ~ NA,
      TRUE ~ t5.6
    ),
    t5.7 = case_when( 
      t5.7%in% c( 9999, 999, 99 )  ~ NA,
      TRUE ~ t5.7
    ) 
  )
#t3.1_1 : t4.3.3  元改成万元单位
dat3.1.1 <- dat3.1.1 %>%
  mutate(
    t5.1  = case_when( 
      t5.1 >=1000 ~ t5.1 /10000,
      TRUE ~ t5.1
    ),
    t5.2  = case_when( 
      t5.2 >=100~ t5.2 /10000,
      TRUE ~ t5.2
    ),
    t5.6 = case_when( 
      t5.6 >=1000 ~ t5.6 /10000,
      TRUE ~ t5.6
    ),
    t5.7 = case_when( 
      t5.7 >=100 ~ t5.7 /10000,
      TRUE ~ t5.7
    ) 
  )


# ### 第二期问卷异常数据检查 --------------------------------------------------------------

# 提取异常值问卷号的函数
extract_outlier_questionnaire_ids <- function(data) {
  
  print("=== 提取异常值问卷号 ===")
  
  # 检查t5.8变量是否存在
  if(!"t5.8" %in% names(data)) {
    print("❌ t5.8变量不存在，无法提取问卷号")
    return(NULL)
  }
  
  # 定义需要检查的医疗费用变量
  medical_vars <- c("t3.4", "t3.5.1.1", "t3.5.1.2", "t3.5.2.1", "t3.5.2.2", 
                    "t3.5.3.1", "t3.5.3.2", "t3.5.4.1", "t3.5.4.2",
                    "t3.5.5", "t3.5.6", "t3.5.7", "t3.5.8",
                    "t3.6.1_1", "t3.6.1.1", "t3.7.1_1", "t3.7.1.1", 
                    "t3.8.1_1", "t3.8.1.1")
  
  # 安全数值转换
  safe_numeric <- function(x) {
    suppressWarnings(as.numeric(as.character(x)))
  }
  
  # 异常值检测函数
  detect_outliers_iqr <- function(x) {
    x_clean <- x[!is.na(x) & x >= 0]
    if(length(x_clean) < 10) return(rep(FALSE, length(x)))
    
    Q1 <- quantile(x_clean, 0.25)
    Q3 <- quantile(x_clean, 0.75)
    IQR <- Q3 - Q1
    lower_bound <- Q1 - 1.5 * IQR
    upper_bound <- Q3 + 1.5 * IQR
    return(x < lower_bound | x > upper_bound)
  }
  
  # 存储异常值记录
  outlier_records <- data.frame(
    问卷号_t5.8 = character(),
    变量名 = character(),
    异常值 = numeric(),
    异常类型 = character(),
    stringsAsFactors = FALSE
  )
  
  # 检查每个医疗费用变量
  for(var in medical_vars) {
    if(var %in% names(data)) {
      var_data <- safe_numeric(data[[var]])
      
      # 检测各种异常情况
      
      # 1. 负值异常
      negative_indices <- which(var_data < 0)
      if(length(negative_indices) > 0) {
        for(idx in negative_indices) {
          outlier_records <- rbind(outlier_records, data.frame(
            问卷号_t5.8 = as.character(data$t5.8[idx]),
            变量名 = var,
            异常值 = var_data[idx],
            异常类型 = "负值",
            stringsAsFactors = FALSE
          ))
        }
      }
      
      # 2. IQR异常值
      iqr_outliers <- detect_outliers_iqr(var_data)
      iqr_indices <- which(iqr_outliers & !is.na(var_data) & var_data >= 0)
      if(length(iqr_indices) > 0) {
        for(idx in iqr_indices) {
          outlier_records <- rbind(outlier_records, data.frame(
            问卷号_t5.8 = as.character(data$t5.8[idx]),
            变量名 = var,
            异常值 = var_data[idx],
            异常类型 = "IQR异常值",
            stringsAsFactors = FALSE
          ))
        }
      }
      
      # 3. 极端高值（>1000万元）
      extreme_indices <- which(var_data >= 1000 & !is.na(var_data))
      if(length(extreme_indices) > 0) {
        for(idx in extreme_indices) {
          outlier_records <- rbind(outlier_records, data.frame(
            问卷号_t5.8 = as.character(data$t5.8[idx]),
            变量名 = var,
            异常值 = var_data[idx],
            异常类型 = "极端高值(>1000万)",
            stringsAsFactors = FALSE
          ))
        }
      }
      
      # 4. Z分数异常（|Z| > 3）
      valid_data <- var_data[!is.na(var_data) & var_data >= 0]
      if(length(valid_data) >= 10) {
        z_scores <- abs((var_data - mean(valid_data)) / sd(valid_data))
        z_outlier_indices <- which(z_scores > 3 & !is.na(var_data) & var_data >= 0)
        if(length(z_outlier_indices) > 0) {
          for(idx in z_outlier_indices) {
            outlier_records <- rbind(outlier_records, data.frame(
              问卷号_t5.8 = as.character(data$t5.8[idx]),
              变量名 = var,
              异常值 = var_data[idx],
              异常类型 = "Z分数异常(>3)",
              stringsAsFactors = FALSE
            ))
          }
        }
      }
    }
  }
  
  # 检查逻辑异常
  print("检查逻辑异常...")
  
  # 个人支付超过总费用的情况
  logic_checks <- list(
    list(total = "t3.5.1.1", personal = "t3.5.1.2", name = "门诊费用逻辑异常"),
    list(total = "t3.5.2.1", personal = "t3.5.2.2", name = "住院费用逻辑异常"),
    list(total = "t3.5.3.1", personal = "t3.5.3.2", name = "药店费用逻辑异常")
  )
  
  for(check in logic_checks) {
    if(all(c(check$total, check$personal) %in% names(data))) {
      total_cost <- safe_numeric(data[[check$total]])
      personal_cost <- safe_numeric(data[[check$personal]])
      
      logic_error_indices <- which(personal_cost > total_cost & !is.na(personal_cost) & !is.na(total_cost))
      if(length(logic_error_indices) > 0) {
        for(idx in logic_error_indices) {
          outlier_records <- rbind(outlier_records, data.frame(
            问卷号_t5.8 = as.character(data$t5.8[idx]),
            变量名 = paste(check$personal, ">", check$total),
            异常值 = personal_cost[idx],
            异常类型 = check$name,
            stringsAsFactors = FALSE
          ))
        }
      }
    }
  }
  
  # 去重并排序
  outlier_records <- outlier_records[!duplicated(outlier_records), ]
  outlier_records <- outlier_records[order(outlier_records$问卷号_t5.8, outlier_records$变量名), ]
  
  return(outlier_records)
}

# 生成异常值汇总报告
generate_outlier_summary <- function(outlier_records) {
  
  if(is.null(outlier_records) || nrow(outlier_records) == 0) {
    print("没有发现异常值")
    return(NULL)
  }
  
  print("\n=== 异常值汇总报告 ===")
  
  # 按异常类型统计
  type_summary <- outlier_records %>%
    group_by(异常类型) %>%
    summarise(
      异常记录数 = n(),
      涉及问卷数 = length(unique(问卷号_t5.8)),
      .groups = 'drop'
    )
  
  print("按异常类型统计：")
  print(type_summary)
  
  # 按变量统计
  var_summary <- outlier_records %>%
    group_by(变量名) %>%
    summarise(
      异常记录数 = n(),
      涉及问卷数 = length(unique(问卷号_t5.8)),
      .groups = 'drop'
    ) %>%
    arrange(desc(异常记录数))
  
  print("\n按变量统计（前10个）：")
  print(head(var_summary, 10))
  
  # 问卷异常情况统计
  questionnaire_summary <- outlier_records %>%
    group_by(问卷号_t5.8) %>%
    summarise(
      异常变量数 = length(unique(变量名)),
      异常记录数 = n(),
      .groups = 'drop'
    ) %>%
    arrange(desc(异常变量数))
  
  print(paste("\n总计发现", nrow(questionnaire_summary), "份问卷存在异常"))
  print("异常最多的前10份问卷：")
  print(head(questionnaire_summary, 10))
  
  return(list(
    type_summary = type_summary,
    var_summary = var_summary,
    questionnaire_summary = questionnaire_summary
  ))
}

#### 第二期—已接受治疗  执行异常值提取
if(exists("dat2.1.1")) {
  print("开始提取异常值问卷号...")
  
  # 提取异常值记录
  outlier_records <- extract_outlier_questionnaire_ids(dat2.1.1)
  
  if(!is.null(outlier_records) && nrow(outlier_records) > 0) {
    
    # 生成汇总报告
    summary_report <- generate_outlier_summary(outlier_records)
    
    # 保存详细结果
    write.csv(outlier_records, "异常值问卷号详细清单.csv", row.names = FALSE, fileEncoding = "UTF-8")
    
     print("保存文件：")
    print("- 异常值问卷号详细清单.csv（详细异常记录）")
 
  } else {
    print("未发现异常值")
  }
  
} else {
  print("❌ dat2.1.1数据不存在")
}
  
### 第二期—未接受治疗 执行异常值提取
if(exists("dat1.1.1")) {
  print("开始提取异常值问卷号...")
  
  # 提取异常值记录
  outlier_records <- extract_outlier_questionnaire_ids(dat1.1.1)
  
  if(!is.null(outlier_records) && nrow(outlier_records) > 0) {
    
    # 生成汇总报告
    summary_report1.1.1 <- generate_outlier_summary(outlier_records)
    
    # 保存详细结果
    write.csv(outlier_records, "dat1.1.1异常值问卷号详细清单.csv", row.names = FALSE, fileEncoding = "UTF-8")
    
    print("保存文件：")
    print("-dat1.1.1 异常值问卷号详细清单.csv（详细异常记录）")
    
  } else {
    print("未发现异常值")
  }
  
} else {
  print("❌ dat1.1.1据不存在")
}
  
 
## 数据异常值修正脚本 -------------------------------------------------------------
### 第二期已接受治疗
correct_data_anomalies <- function(data) {
  
  print("=== 开始数据异常值修正 ===")
  
  # 安全数值转换
  safe_numeric <- function(x) {
    suppressWarnings(as.numeric(as.character(x)))
  }
  
  # 修正计数器
  correction_count <- list(
    extreme_values = 0,
    logic_swaps = 0
  )
  
  # 1. 修正极端高值（>1000万元）
  print("修正极端高值（>100万元）...")
  
  medical_vars <- c("t3.4",
                    "t3.5.1.1", "t3.5.1.2", 
                    "t3.5.2.1", "t3.5.2.2", 
                    "t3.5.3.1", "t3.5.3.2",
                    "t3.5.4.1", "t3.5.4.2")

  
  for(var in medical_vars) {
    if(var %in% names(data)) {
      var_data <- safe_numeric(data[[var]])
      
      # 找到极端高值
      extreme_indices <- which(var_data > 1000 & !is.na(var_data))
      
      if(length(extreme_indices) > 0) {
        # 记录修正前的值
        before_values <- var_data[extreme_indices]
        
        # 修正：除以10000
        data[[var]][extreme_indices] <- var_data[extreme_indices] / 10000

        # 记录修正
        correction_count$extreme_values <- correction_count$extreme_values + length(extreme_indices)
        
        cat("变量", var, ": 修正", length(extreme_indices), "个极端高值\n")
        
        # 显示修正示例
        if(length(extreme_indices) <= 3) {
          for(i in 1:length(extreme_indices)) {
            idx <- extreme_indices[i]
            questionnaire_id <- if("t5.8" %in% names(data)) data$t5.8[idx] else paste("行", idx)
            cat("  问卷", questionnaire_id, ": ", before_values[i], "万元 → ", 
                round(before_values[i] / 10000, 4), "万元\n")
          }
        }
      }
    }
  }
  
  # 2. 修正逻辑异常（总费用和个人支付对调）
  print("\n修正逻辑异常（费用对调）...")
  
  logic_pairs <- list(
    list(total = "t3.5.1.1", personal = "t3.5.1.2", name = "门诊费用"),
    list(total = "t3.5.2.1", personal = "t3.5.2.2", name = "住院费用"),
    list(total = "t3.5.3.1", personal = "t3.5.3.2", name = "药店费用"),
    list(total = "t3.5.4.1", personal = "t3.5.4.2", name = "未分类费用") 
  )
  
  for(pair in logic_pairs) {
    if(all(c(pair$total, pair$personal) %in% names(data))) {
      total_data <- safe_numeric(data[[pair$total]])
      personal_data <- safe_numeric(data[[pair$personal]])
      
      # 找到逻辑错误：个人支付 > 总费用
      error_indices <- which(personal_data > total_data & 
                               !is.na(personal_data) & !is.na(total_data) &
                               personal_data > 0 & total_data > 0)
      
      if(length(error_indices) > 0) {
        # 对调数值
        temp_values <- data[[pair$total]][error_indices]
        data[[pair$total]][error_indices] <- data[[pair$personal]][error_indices]
        data[[pair$personal]][error_indices] <- temp_values
        
        correction_count$logic_swaps <- correction_count$logic_swaps + length(error_indices)
        
        cat(pair$name, "逻辑异常: 对调", length(error_indices), "个记录\n")
        
        # 显示对调示例
        if(length(error_indices) <= 3) {
          for(i in 1:length(error_indices)) {
            idx <- error_indices[i]
            questionnaire_id <- if("t5.8" %in% names(data)) data$t5.8[idx] else paste("行", idx)
            cat("  问卷", questionnaire_id, ": 总费用", personal_data[idx], 
                "万元 ↔ 个人支付", total_data[idx], "万元\n")
          }
        }
      }
    }
  }
  
  # 3. 生成修正报告
  print("\n=== 修正报告 ===")
  cat("极端高值修正数量:", correction_count$extreme_values, "个\n")
  cat("逻辑异常对调数量:", correction_count$logic_swaps, "个\n")
  cat("总修正数量:", correction_count$extreme_values + correction_count$logic_swaps, "个\n")
  
  return(data)
}

# 修正前后对比函数
compare_before_after <- function(data_before, data_after) {
  
  print("\n=== 修正前后对比 ===")
  
  # 安全数值转换
  safe_numeric <- function(x) {
    suppressWarnings(as.numeric(as.character(x)))
  }
  
  # 检查几个关键变量的变化
  key_vars <- c("t3.4", "t3.5.1.1", "t3.5.1.2", "t3.5.2.1", "t3.5.2.2", "t3.5.3.1", "t3.5.3.2", "t3.5.4.1", "t3.5.4.2")
  
  for(var in key_vars) {
    if(var %in% names(data_before) && var %in% names(data_after)) {
      before_data <- safe_numeric(data_before[[var]])
      after_data <- safe_numeric(data_after[[var]])
      
      # 统计变化
      changed_indices <- which(before_data != after_data | 
                                 (is.na(before_data) != is.na(after_data)))
      
      if(length(changed_indices) > 0) {
        cat("\n变量", var, ":")
        cat(" 修正", length(changed_indices), "个值\n")
        
        # 显示统计变化
        before_stats <- summary(before_data[before_data > 0 & !is.na(before_data)])
        after_stats <- summary(after_data[after_data > 0 & !is.na(after_data)])
        
        cat("  修正前最大值:", round(before_stats["Max."], 2), "万元\n")
        cat("  修正后最大值:", round(after_stats["Max."], 2), "万元\n")
      }
    }
  }
}

# 验证修正结果
validate_corrections <- function(data) {
  
  print("\n=== 验证修正结果 ===")
  
  # 安全数值转换
  safe_numeric <- function(x) {
    suppressWarnings(as.numeric(as.character(x)))
  }
  
  validation_results <- list()
  
  # 1. 检查是否还有极端高值
  medical_vars <- c("t3.4", "t3.5.1.1", "t3.5.1.2", "t3.5.2.1", "t3.5.2.2", "t3.5.3.1", "t3.5.3.2", "t3.5.4.1", "t3.5.4.2")
  extreme_count <- 0
  
  for(var in medical_vars) {
    if(var %in% names(data)) {
      var_data <- safe_numeric(data[[var]])
      extreme_count <- extreme_count + sum(var_data > 100, na.rm = TRUE)
    }
  }
  
  validation_results$remaining_extreme <- extreme_count
  cat("剩余极端高值(>100万):", extreme_count, "个\n")
  
  # 2. 检查是否还有逻辑异常
  logic_errors <- 0
  logic_pairs <- list(
    list(total = "t3.5.1.1", personal = "t3.5.1.2"),
    list(total = "t3.5.2.1", personal = "t3.5.2.2"),
    list(total = "t3.5.3.1", personal = "t3.5.3.2"),
    list(total = "t3.5.4.1", personal = "t3.5.4.2")
  )
  
  for(pair in logic_pairs) {
    if(all(c(pair$total, pair$personal) %in% names(data))) {
      total_data <- safe_numeric(data[[pair$total]])
      personal_data <- safe_numeric(data[[pair$personal]])
      
      errors <- sum(personal_data > total_data & 
                      !is.na(personal_data) & !is.na(total_data), na.rm = TRUE)
      logic_errors <- logic_errors + errors
    }
  }
  
  validation_results$remaining_logic_errors <- logic_errors
  cat("剩余逻辑异常:", logic_errors, "个\n")
  
  if(extreme_count == 0 && logic_errors == 0) {
    cat("✅ 所有目标异常已修正完成\n")
  } else {
    cat("⚠️ 仍有异常需要进一步处理\n")
  }
  
  return(validation_results)
}

# 执行数据修正
if(exists("dat2.1.1")) {
  print("开始执行数据异常值修正...")
  
  # 备份原始数据
  dat2.1.1_backup <- dat2.1.1
  
  # 执行修正
  dat2.1.1_corrected <- correct_data_anomalies(dat2.1.1)
  
  # 对比修正前后
  compare_before_after(dat2.1.1_backup, dat2.1.1_corrected)
  
  # 验证修正结果
  validation_results <- validate_corrections(dat2.1.1_corrected)
  
  # 更新数据
  dat2.1.1 <- dat2.1.1_corrected

  print("\n✅ 数据修正完成！")
  print("修正内容：")
  print("1. 极端高值(>1000万元) ÷ 10000")
  print("2. 逻辑异常费用对调（个人支付与总费用）")
  print("3. 修正后数据已保存为'修正后数据.csv'")
  
} else {
  print("❌ dat2.1.1数据不存在")
}

### 第二期未接受治疗
correct_data111_anomalies <- function(data) {
  
  print("=== 开始数据异常值修正 ===")
  
  # 安全数值转换
  safe_numeric <- function(x) {
    suppressWarnings(as.numeric(as.character(x)))
  }
  
  # 修正计数器
  correction_count <- list(
    extreme_values = 0,
    logic_swaps = 0
  )
  
  # 1. 修正极端高值（>100万元）
  print("修正极端高值（>100万元）...")
  
  medical_vars <- c("t3.4",
                    "t3.5.1.1", "t3.5.1.2", 
                    "t3.5.2.1", "t3.5.2.2", 
                    "t3.5.3.1", "t3.5.3.2",
                    "t3.5.4.1", "t3.5.4.2")
  
  
  for(var in medical_vars) {
    if(var %in% names(data)) {
      var_data <- safe_numeric(data[[var]])
      
      # 找到极端高值
      extreme_indices <- which(var_data > 100 & !is.na(var_data))
      
      if(length(extreme_indices) > 0) {
        # 记录修正前的值
        before_values <- var_data[extreme_indices]
        
        # 修正：除以10000
        data[[var]][extreme_indices] <- var_data[extreme_indices] / 10000
        
        # 记录修正
        correction_count$extreme_values <- correction_count$extreme_values + length(extreme_indices)
        
        cat("变量", var, ": 修正", length(extreme_indices), "个极端高值\n")
        
        # 显示修正示例
        if(length(extreme_indices) <= 3) {
          for(i in 1:length(extreme_indices)) {
            idx <- extreme_indices[i]
            questionnaire_id <- if("t5.8" %in% names(data)) data$t5.8[idx] else paste("行", idx)
            cat("  问卷", questionnaire_id, ": ", before_values[i], "万元 → ", 
                round(before_values[i] / 10000, 4), "万元\n")
          }
        }
      }
    }
  }
  
  # 2. 修正逻辑异常（总费用和个人支付对调）
  print("\n修正逻辑异常（费用对调）...")
  
  logic_pairs <- list(
    list(total = "t3.5.1.1", personal = "t3.5.1.2", name = "门诊费用"),
    list(total = "t3.5.2.1", personal = "t3.5.2.2", name = "住院费用"),
    list(total = "t3.5.3.1", personal = "t3.5.3.2", name = "药店费用"),
    list(total = "t3.5.4.1", personal = "t3.5.4.2", name = "未分类费用") 
  )
  
  for(pair in logic_pairs) {
    if(all(c(pair$total, pair$personal) %in% names(data))) {
      total_data <- safe_numeric(data[[pair$total]])
      personal_data <- safe_numeric(data[[pair$personal]])
      
      # 找到逻辑错误：个人支付 > 总费用
      error_indices <- which(personal_data > total_data & 
                               !is.na(personal_data) & !is.na(total_data) &
                               personal_data > 0 & total_data > 0)
      
      if(length(error_indices) > 0) {
        # 对调数值
        temp_values <- data[[pair$total]][error_indices]
        data[[pair$total]][error_indices] <- data[[pair$personal]][error_indices]
        data[[pair$personal]][error_indices] <- temp_values
        
        correction_count$logic_swaps <- correction_count$logic_swaps + length(error_indices)
        
        cat(pair$name, "逻辑异常: 对调", length(error_indices), "个记录\n")
        
        # 显示对调示例
        if(length(error_indices) <= 3) {
          for(i in 1:length(error_indices)) {
            idx <- error_indices[i]
            questionnaire_id <- if("t5.8" %in% names(data)) data$t5.8[idx] else paste("行", idx)
            cat("  问卷", questionnaire_id, ": 总费用", personal_data[idx], 
                "万元 ↔ 个人支付", total_data[idx], "万元\n")
          }
        }
      }
    }
  }
  
  # 3. 生成修正报告
  print("\n=== 修正报告 ===")
  cat("极端高值修正数量:", correction_count$extreme_values, "个\n")
  cat("逻辑异常对调数量:", correction_count$logic_swaps, "个\n")
  cat("总修正数量:", correction_count$extreme_values + correction_count$logic_swaps, "个\n")
  
  return(data)
}

if(exists("dat1.1.1")) {
  print("开始执行数据异常值修正...")
  
  # 备份原始数据
  dat1.1.1_backup <- dat1.1.1
  
  # 执行修正
  dat1.1.1_corrected <- correct_data111_anomalies(dat1.1.1)
  
  # 对比修正前后
  compare_before_after(dat1.1.1_backup, dat1.1.1_corrected)
  
  # 验证修正结果
  validation_results <- validate_corrections(dat1.1.1_corrected)
  
  # 更新数据
  dat1.1.1 <- dat1.1.1_corrected
  
  print("\n✅ 数据修正完成！")
  print("修正内容：")
  print("1. 极端高值(>100万元) ÷ 10000")
  print("2. 逻辑异常费用对调（个人支付与总费用）")
  print("3. 修正后数据已保存为'修正后数据.csv'")
  
} else {
  print("❌ dat1.1.1数据不存在")
}



# # ============================================================================
# # 生成医疗费用分项汇总报告
# # ============================================================================
#第二期已接受治疗
calculate_medical_expenditure_by_components <- function(data) {
  
  print("=== 根据医疗分项计算总费用、报销费用和个人自付费用 ===")
  
  # 安全数值转换函数
  safe_numeric <- function(x, var_name = "unknown") {
    if(is.null(x) || length(x) == 0) {
      return(rep(0, nrow(data)))
    }
    
    result <- suppressWarnings(as.numeric(as.character(x)))
    result[is.na(result) | result < 0] <- 0
    return(result)
  }
  
  # 安全获取变量值
  safe_get <- function(var_name) {
    if(var_name %in% names(data)) {
      return(safe_numeric(data[[var_name]], var_name))
    } else {
      return(rep(0, nrow(data)))
    }
  }
  
 data <- 
  data %>%
    mutate(
      # ========================================================================
      # 第一部分：医保平台费用（3.5.1-3.5.4）
      # ========================================================================
      
      # 1.1 医保平台总费用
      outpatient_total = safe_get("t3.5.1.1"),      # 门诊总费用
      inpatient_total = safe_get("t3.5.2.1"),       # 住院总费用
      pharmacy_total = safe_get("t3.5.3.1"),        # 药店总费用
      unclassified_total = safe_get("t3.5.4.1"),    # 未分类总费用
      
      platform_total_cost = outpatient_total + inpatient_total + pharmacy_total + unclassified_total,
      
      # 1.2 医保平台个人支付
      outpatient_personal = safe_get("t3.5.1.2"),   # 门诊个人支付
      inpatient_personal = safe_get("t3.5.2.2"),    # 住院个人支付
      pharmacy_personal = safe_get("t3.5.3.2"),     # 药店个人支付
      unclassified_personal = safe_get("t3.5.4.2"), # 未分类个人支付
      
      platform_personal_cost = outpatient_personal + inpatient_personal + pharmacy_personal + unclassified_personal,
      
      # 1.3 医保平台医保报销（总费用 - 个人支付）
      platform_insurance_reimbursement =   platform_total_cost - platform_personal_cost ,
      
      # 1.4 医保平台额外报销
      platform_public_reimbursement = safe_get("t3.5.5"),      # 公费医疗报销
      platform_commercial_supplement = safe_get("t3.5.6"),     # 商业补充医疗保险报销
      platform_commercial_insurance = safe_get("t3.5.7"),      # 商业保险报销
      platform_other_reimbursement = safe_get("t3.5.8"),       # 其他途径报销
      
      platform_additional_reimbursement = platform_public_reimbursement + platform_commercial_supplement +
        platform_commercial_insurance + platform_other_reimbursement,
      
      # 1.5 医保平台总报销
      platform_total_reimbursement = platform_insurance_reimbursement + platform_additional_reimbursement,
      
      # 1.6 医保平台额外报销限制（不能超过个人支付）
      platform_additional_reimbursement_limited = pmin(platform_additional_reimbursement, platform_personal_cost),
      
      # 1.7 医保平台最终个人自付（确保非负）
      platform_final_personal = pmax(0, platform_personal_cost - platform_additional_reimbursement_limited),
      
      # ========================================================================
      # 第二部分：私立医院/境外医疗费用（3.6）
      # ========================================================================
      
      # 2.1 判断是否有私立医院/境外医疗
      has_private_medical = case_when(
        "t3.6-是" %in% names(data) ~ safe_get("t3.6-是"),
        "t3.6" %in% names(data) ~ safe_get("t3.6"),
        TRUE ~ 0
      ),
      
      # 2.2 判断是否可以报销
      private_can_reimburse = case_when(
        "t3.6.1-是" %in% names(data) ~ safe_get("t3.6.1-是"),
        "t3.6.1" %in% names(data) ~ safe_get("t3.6.1"),
        TRUE ~ 0
      ),
      
      # 2.3 私立医院/境外医疗总费用
      private_total_cost = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1"),
        has_private_medical == 1 & private_can_reimburse == 0 ~ safe_get("t3.6.1_1"),
        TRUE ~ 0
      ),
      
      # 2.4 私立医院/境外医疗报销
      private_public_reimbursement = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.1"),
        TRUE ~ 0
      ),
      private_commercial_supplement = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.2"),
        TRUE ~ 0
      ),
      private_commercial_insurance = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.3"),
        TRUE ~ 0
      ),
      private_other_reimbursement = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.4"),
        TRUE ~ 0
      ),
      
      private_total_reimbursement = private_public_reimbursement + private_commercial_supplement +
        private_commercial_insurance + private_other_reimbursement,
      
      # 2.5 私立医院报销限制（不能超过总费用）
      private_total_reimbursement_limited = pmin(private_total_reimbursement, private_total_cost),
      
      # 2.6 私立医院/境外医疗最终个人自付（确保非负）
      private_final_personal = pmax(0, private_total_cost - private_total_reimbursement_limited),
      
      # ========================================================================
      # 第三部分：自费药物费用（3.7）
      # ========================================================================
      
      # 3.1 判断是否有自费药物
      has_selfpaid_drug = case_when(
        "t3.7-是" %in% names(data) ~ safe_get("t3.7-是"),
        "t3.7" %in% names(data) ~ safe_get("t3.7"),
        TRUE ~ 0
      ),
      
      # 3.2 判断是否可以报销
      drug_can_reimburse = case_when(
        "t3.7.1-是" %in% names(data) ~ safe_get("t3.7.1-是"),
        "t3.7.1" %in% names(data) ~ safe_get("t3.7.1"),
        TRUE ~ 0
      ),
      
      # 3.3 自费药物总费用
      selfpaid_drug_total_cost = case_when(
        has_selfpaid_drug == 1 & drug_can_reimburse == 1 ~ safe_get("t3.7.1.1"),
        has_selfpaid_drug == 1 & drug_can_reimburse == 0 ~ safe_get("t3.7.1_1"),
        TRUE ~ 0
      ),
      
      # 3.4 自费药物报销
      drug_public_reimbursement = case_when(
        has_selfpaid_drug == 1 & drug_can_reimburse == 1 ~ safe_get("t3.7.1.1.1"),
        TRUE ~ 0
      ),
      drug_commercial_supplement = case_when(
        has_selfpaid_drug == 1 & drug_can_reimburse == 1 ~ safe_get("t3.7.1.1.2"),
        TRUE ~ 0
      ),
      drug_commercial_insurance = case_when(
        has_selfpaid_drug == 1 & drug_can_reimburse == 1 ~ safe_get("t3.7.1.1.3"),
        TRUE ~ 0
      ),
      drug_other_reimbursement = case_when(
        has_selfpaid_drug == 1 & drug_can_reimburse == 1 ~ safe_get("t3.7.1.1.4"),
        TRUE ~ 0
      ),
      
      drug_total_reimbursement = drug_public_reimbursement + drug_commercial_supplement +
        drug_commercial_insurance + drug_other_reimbursement,
      
      # 3.5 自费药物报销限制（不能超过总费用）
      drug_total_reimbursement_limited = pmin(drug_total_reimbursement, selfpaid_drug_total_cost),
      
      # 3.6 自费药物最终个人自付（确保非负）
      selfpaid_drug_final_personal = pmax(0, selfpaid_drug_total_cost - drug_total_reimbursement_limited),
      
      # ========================================================================
      # 第四部分：基因检测等费用（3.8）
      # ========================================================================
      
      # 4.1 判断是否有基因检测
      has_genetic_test = case_when(
        "t3.8-是" %in% names(data) ~ safe_get("t3.8-是"),
        "t3.8" %in% names(data) ~ safe_get("t3.8"),
        TRUE ~ 0
      ),
      
      # 4.2 判断是否可以报销
      genetic_can_reimburse = case_when(
        "t3.8.1-是" %in% names(data) ~ safe_get("t3.8.1-是"),
        "t3.8.1" %in% names(data) ~ safe_get("t3.8.1"),
        TRUE ~ 0
      ),
      
      # 4.3 基因检测总费用
      genetic_test_total_cost = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.8.1.1"),
        has_genetic_test == 1 & genetic_can_reimburse == 0 ~ safe_get("t3.8.1_1"),
        TRUE ~ 0
      ),
      
      # 4.4 基因检测报销
      genetic_public_reimbursement = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.8.1.1.1"),
        TRUE ~ 0
      ),
      genetic_commercial_supplement = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.8.1.1.2"),
        TRUE ~ 0
      ),
      genetic_commercial_insurance = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.8.1.1.3"),
        TRUE ~ 0
      ),
      genetic_other_reimbursement = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.8.1.1.4"),
        TRUE ~ 0
      ),
      
      genetic_total_reimbursement = genetic_public_reimbursement + genetic_commercial_supplement +
        genetic_commercial_insurance + genetic_other_reimbursement,
      
      # 4.5 基因检测报销限制（不能超过总费用）
      genetic_total_reimbursement_limited = pmin(genetic_total_reimbursement, genetic_test_total_cost),
      
      # 4.6 基因检测最终个人自付（确保非负）
      genetic_test_final_personal = pmax(0, genetic_test_total_cost - genetic_total_reimbursement_limited),
      
      # ========================================================================
      # 第五部分：汇总计算
      # ========================================================================
      
      # 5.1 总医疗费用（所有分项总和）
      total_medical_cost_calculated = platform_total_cost + private_total_cost +
        selfpaid_drug_total_cost + genetic_test_total_cost,
      
      # 5.2 总报销费用（使用限制后的报销金额）
      total_reimbursement_calculated = (platform_insurance_reimbursement + platform_additional_reimbursement_limited) +
        private_total_reimbursement_limited + drug_total_reimbursement_limited + genetic_total_reimbursement_limited,
      
      # 5.3 总个人自付费用（所有分项个人自付总和）
      total_personal_expenditure_calculated = platform_final_personal + private_final_personal +
        selfpaid_drug_final_personal + genetic_test_final_personal,
      
      # 5.4 验证：总费用 = 总报销 + 总个人自付
      calculation_check = total_medical_cost_calculated - (total_reimbursement_calculated + total_personal_expenditure_calculated),
      
      # 非直接医疗费用（交通、住宿、营养等 - 基于问卷相关变量）
      indirect_medical_cost = safe_numeric(
        if("t3.9" %in% names(data)) t3.9 else 0  # 交通费
      ) + safe_numeric(
        if("t3.10" %in% names(data)) t3.10 else 0  # 住宿费
      ) + safe_numeric(
        if("t3.11" %in% names(data)) t3.11 else 0  # 餐饮费
      ) + safe_numeric(
        if("t3.12" %in% names(data)) t3.12 else 0  # 营养费
      )+ safe_numeric(
        if("t3.13" %in% names(data)) t3.13 else 0  # 专职护工
      )+ safe_numeric(
        if("t3.14" %in% names(data)) t3.14 else 0  # 其他费用
      ) + safe_numeric(
        if("t3.15" %in% names(data)) t3.15 else 0  # 其他费用
      ),
 
      # 5.5 与t3.4对比
      t3_4_value = safe_get("t3.4"),
      difference_with_t34_total = total_medical_cost_calculated - t3_4_value,
      difference_with_t34_personal = total_personal_expenditure_calculated - t3_4_value,
      
      # 5.6 报销超限检测
      platform_reimbursement_excess = platform_additional_reimbursement - platform_additional_reimbursement_limited,
      private_reimbursement_excess = private_total_reimbursement - private_total_reimbursement_limited,
      drug_reimbursement_excess = drug_total_reimbursement - drug_total_reimbursement_limited,
      genetic_reimbursement_excess = genetic_total_reimbursement - genetic_total_reimbursement_limited,
      
      total_reimbursement_excess = platform_reimbursement_excess + private_reimbursement_excess +
        drug_reimbursement_excess + genetic_reimbursement_excess,
      
      # 5.7 数据质量标记
      data_quality_flag = case_when(
        abs(calculation_check) > 0.01 ~ "计算不平衡",
        total_medical_cost_calculated <= 0 ~ "无医疗费用",
        total_personal_expenditure_calculated < 0 ~ "个人自付为负",
        total_reimbursement_excess > 0.01 ~ "存在报销超限",
        TRUE ~ "计算正常"
      )
    )
  # 统计报销超限情况
  excess_summary <-   data %>%
    summarise(
      总记录数 = n(),
      医保平台报销超限记录数 = sum(platform_reimbursement_excess > 0.01, na.rm = TRUE),
      私立医院报销超限记录数 = sum(private_reimbursement_excess > 0.01, na.rm = TRUE),
      自费药物报销超限记录数 = sum(drug_reimbursement_excess > 0.01, na.rm = TRUE),
      基因检测报销超限记录数 = sum(genetic_reimbursement_excess > 0.01, na.rm = TRUE),
      总报销超限记录数 = sum(total_reimbursement_excess > 0.01, na.rm = TRUE),
      医保平台超限总金额 = sum(platform_reimbursement_excess, na.rm = TRUE),
      私立医院超限总金额 = sum(private_reimbursement_excess, na.rm = TRUE),
      自费药物超限总金额 = sum(drug_reimbursement_excess, na.rm = TRUE),
      基因检测超限总金额 = sum(genetic_reimbursement_excess, na.rm = TRUE),
      总超限金额 = sum(total_reimbursement_excess, na.rm = TRUE)
    )
  
  print("\n=== 报销超限统计报告 ===")
  print(excess_summary)
  
  if(excess_summary$总报销超限记录数 > 0) {
    print(paste("\n⚠️ 发现", excess_summary$总报销超限记录数, "条记录存在报销超限"))
    print(paste("总超限金额:", round(excess_summary$总超限金额, 2), "万元"))
    print("已自动调整为不超过相应费用总额，确保个人支付费用非负")
  } else {
    print("\n✅ 无报销超限记录，所有个人支付费用均为非负值")
  }
  return(data)
  
}
generate_medical_expenditure_summary <- function(data) {

  print("=== 生成医疗费用分项汇总报告 ===")

  # 创建汇总表
  summary_table <- data.frame(
    费用分项 = character(),
    总费用_万元 = numeric(),
    报销费用_万元 = numeric(),
    个人自付_万元 = numeric(),
    个人自付比例_percent = numeric(),
    有效案例数 = numeric(),
    stringsAsFactors = FALSE
  )

  # 定义分项
  components <- list(
    "医保平台费用" = list(
      total = "platform_total_cost",
      reimbursement = "platform_total_reimbursement",
      personal = "platform_final_personal"
    ),
    "私立医院/境外医疗" = list(
      total = "private_total_cost",
      reimbursement = "private_total_reimbursement",
      personal = "private_final_personal"
    ),
    "自费药物费用" = list(
      total = "selfpaid_drug_total_cost",
      reimbursement = "drug_total_reimbursement",
      personal = "selfpaid_drug_final_personal"
    ),
    "基因检测费用" = list(
      total = "genetic_test_total_cost",
      reimbursement = "genetic_total_reimbursement",
      personal = "genetic_test_final_personal"
    )
  )

  # 计算各分项统计
  for(comp_name in names(components)) {
    comp <- components[[comp_name]]

    if(all(c(comp$total, comp$reimbursement, comp$personal) %in% names(data))) {

      # 筛选有效案例（总费用>0）
      valid_cases <- data[[comp$total]] > 0 & !is.na(data[[comp$total]])

      if(sum(valid_cases) > 0) {
        total_sum <- sum(data[[comp$total]][valid_cases], na.rm = TRUE)
        reimb_sum <- sum(data[[comp$reimbursement]][valid_cases], na.rm = TRUE)
        personal_sum <- sum(data[[comp$personal]][valid_cases], na.rm = TRUE)
        personal_rate <- ifelse(total_sum > 0, personal_sum / total_sum * 100, 0)

        summary_table <- rbind(summary_table, data.frame(
          费用分项 = comp_name,
          总费用_万元 = round(total_sum, 2),
          报销费用_万元 = round(reimb_sum, 2),
          个人自付_万元 = round(personal_sum, 2),
          个人自付比例_percent = round(personal_rate, 1),
          有效案例数 = sum(valid_cases),
          stringsAsFactors = FALSE
        ))
      }
    }
  }

  # 添加总计行
  if(all(c("total_medical_cost_calculated", "total_reimbursement_calculated",
           "total_personal_expenditure_calculated") %in% names(data))) {

    valid_total <- data$total_medical_cost_calculated > 0 & !is.na(data$total_medical_cost_calculated)

    if(sum(valid_total) > 0) {
      total_cost_sum <- sum(data$total_medical_cost_calculated[valid_total], na.rm = TRUE)
      total_reimb_sum <- sum(data$total_reimbursement_calculated[valid_total], na.rm = TRUE)
      total_personal_sum <- sum(data$total_personal_expenditure_calculated[valid_total], na.rm = TRUE)
      total_personal_rate <- ifelse(total_cost_sum > 0, total_personal_sum / total_cost_sum * 100, 0)

      summary_table <- rbind(summary_table, data.frame(
        费用分项 = "总计",
        总费用_万元 = round(total_cost_sum, 2),
        报销费用_万元 = round(total_reimb_sum, 2),
        个人自付_万元 = round(total_personal_sum, 2),
        个人自付比例_percent = round(total_personal_rate, 1),
        有效案例数 = sum(valid_total),
        stringsAsFactors = FALSE
      ))
    }
  }

  return(summary_table)
}

#第二期未接受治疗
calculate_medical_expenditure_by_componentsdat111 <- function(data) {
  
  print("=== 根据医疗分项计算总费用、报销费用和个人自付费用 ===")
  
  # 安全数值转换函数
  safe_numeric <- function(x, var_name = "unknown") {
    if(is.null(x) || length(x) == 0) {
      return(rep(0, nrow(data)))
    }
    
    result <- suppressWarnings(as.numeric(as.character(x)))
    result[is.na(result) | result < 0] <- 0
    return(result)
  }
  
  # 安全获取变量值
  safe_get <- function(var_name) {
    if(var_name %in% names(data)) {
      return(safe_numeric(data[[var_name]], var_name))
    } else {
      return(rep(0, nrow(data)))
    }
  }
  
  data <- 
    data %>%
    mutate(
      # ========================================================================
      # 第一部分：医保平台费用（3.5.1-3.5.4）
      # ========================================================================
      
      # 1.1 医保平台总费用
      outpatient_total = safe_get("t3.5.1.1"),      # 门诊总费用
      inpatient_total = safe_get("t3.5.2.1"),       # 住院总费用
      pharmacy_total = safe_get("t3.5.3.1"),        # 药店总费用
      unclassified_total = safe_get("t3.5.4.1"),    # 未分类总费用
      
      platform_total_cost = outpatient_total + inpatient_total + pharmacy_total + unclassified_total,
      
      # 1.2 医保平台个人支付
      outpatient_personal = safe_get("t3.5.1.2"),   # 门诊个人支付
      inpatient_personal = safe_get("t3.5.2.2"),    # 住院个人支付
      pharmacy_personal = safe_get("t3.5.3.2"),     # 药店个人支付
      unclassified_personal = safe_get("t3.5.4.2"), # 未分类个人支付
      
      platform_personal_cost = outpatient_personal + inpatient_personal + pharmacy_personal + unclassified_personal,
      
      # 1.3 医保平台医保报销（总费用 - 个人支付）
      platform_insurance_reimbursement =   platform_total_cost - platform_personal_cost ,
      
      # 1.4 医保平台额外报销
      platform_public_reimbursement = safe_get("t3.5.5"),      # 公费医疗报销
      platform_commercial_supplement = safe_get("t3.5.6"),     # 商业补充医疗保险报销
      platform_commercial_insurance = safe_get("t3.5.7"),      # 商业保险报销
      platform_other_reimbursement = safe_get("t3.5.8"),       # 其他途径报销
      
      platform_additional_reimbursement = platform_public_reimbursement + platform_commercial_supplement +
        platform_commercial_insurance + platform_other_reimbursement,
      
      # 1.5 医保平台总报销
      platform_total_reimbursement = platform_insurance_reimbursement + platform_additional_reimbursement,
      
      # 1.6 医保平台额外报销限制（不能超过个人支付）
      platform_additional_reimbursement_limited = pmin(platform_additional_reimbursement, platform_personal_cost),
      
      # 1.7 医保平台最终个人自付（确保非负）
      platform_final_personal = pmax(0, platform_personal_cost - platform_additional_reimbursement_limited),
      
      # ========================================================================
      # 第二部分：私立医院/境外医疗费用（3.6）
      # ========================================================================
      
      # 2.1 判断是否有私立医院/境外医疗
      has_private_medical = case_when(
        "t3.6-是" %in% names(data) ~ safe_get("t3.6-是"),
        "t3.6" %in% names(data) ~ safe_get("t3.6"),
        TRUE ~ 0
      ),
      
      # 2.2 判断是否可以报销
      private_can_reimburse = case_when(
        "t3.6.1-是" %in% names(data) ~ safe_get("t3.6.1-是"),
        "t3.6.1" %in% names(data) ~ safe_get("t3.6.1"),
        TRUE ~ 0
      ),
      
      # 2.3 私立医院/境外医疗总费用
      private_total_cost = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1"),
        has_private_medical == 1 & private_can_reimburse == 0 ~ safe_get("t3.6.1_1"),
        TRUE ~ 0
      ),
      
      # 2.4 私立医院/境外医疗报销
      private_public_reimbursement = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.1"),
        TRUE ~ 0
      ),
      private_commercial_supplement = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.2"),
        TRUE ~ 0
      ),
      private_commercial_insurance = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.3"),
        TRUE ~ 0
      ),
      private_other_reimbursement = case_when(
        has_private_medical == 1 & private_can_reimburse == 1 ~ safe_get("t3.6.1.1.4"),
        TRUE ~ 0
      ),
      
      private_total_reimbursement = private_public_reimbursement + private_commercial_supplement +
        private_commercial_insurance + private_other_reimbursement,
      
      # 2.5 私立医院报销限制（不能超过总费用）
      private_total_reimbursement_limited = pmin(private_total_reimbursement, private_total_cost),
      
      # 2.6 私立医院/境外医疗最终个人自付（确保非负）
      private_final_personal = pmax(0, private_total_cost - private_total_reimbursement_limited),
      
      
      # ========================================================================
      # 第四部分：基因检测等费用（3.7）
      # ========================================================================
      
      # 4.1 判断是否有基因检测
      has_genetic_test = case_when(
        "t3.7-是" %in% names(data) ~ safe_get("t3.7-是"),
        "t3.7" %in% names(data) ~ safe_get("t3.7"),
        TRUE ~ 0
      ),
      
      # 4.2 判断是否可以报销
      genetic_can_reimburse = case_when(
        "t3.7.1-是" %in% names(data) ~ safe_get("t3.7.1-是"),
        "t3.7.1" %in% names(data) ~ safe_get("t3.7.1"),
        TRUE ~ 0
      ),
      
      # 4.3 基因检测总费用
      genetic_test_total_cost = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.7.1.1"),
        has_genetic_test == 1 & genetic_can_reimburse == 0 ~ safe_get("t3.7.1_1"),
        TRUE ~ 0
      ),
      
      # 4.4 基因检测报销
      genetic_public_reimbursement = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.7.1.1.1"),
        TRUE ~ 0
      ),
      genetic_commercial_supplement = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.7.1.1.2"),
        TRUE ~ 0
      ),
      genetic_commercial_insurance = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.7.1.1.3"),
        TRUE ~ 0
      ),
      genetic_other_reimbursement = case_when(
        has_genetic_test == 1 & genetic_can_reimburse == 1 ~ safe_get("t3.7.1.1.4"),
        TRUE ~ 0
      ),
      
      genetic_total_reimbursement = genetic_public_reimbursement + genetic_commercial_supplement +
        genetic_commercial_insurance + genetic_other_reimbursement,
      
      # 4.5 基因检测报销限制（不能超过总费用）
      genetic_total_reimbursement_limited = pmin(genetic_total_reimbursement, genetic_test_total_cost),
      
      # 4.6 基因检测最终个人自付（确保非负）
      genetic_test_final_personal = pmax(0, genetic_test_total_cost - genetic_total_reimbursement_limited),
      
      # ========================================================================
      # 第五部分：汇总计算
      # ========================================================================
      
      # 5.1 总医疗费用（所有分项总和）
      total_medical_cost_calculated = platform_total_cost + private_total_cost +
         genetic_test_total_cost,
      
      # 5.2 总报销费用（使用限制后的报销金额）
      total_reimbursement_calculated = (platform_insurance_reimbursement + platform_additional_reimbursement_limited) +
        private_total_reimbursement_limited + genetic_total_reimbursement_limited,
      
      # 5.3 总个人自付费用（所有分项个人自付总和）
      total_personal_expenditure_calculated = platform_final_personal + private_final_personal +
         genetic_test_final_personal,
      
      # 5.4 验证：总费用 = 总报销 + 总个人自付
      calculation_check = total_medical_cost_calculated - (total_reimbursement_calculated + total_personal_expenditure_calculated),
      
      # 非直接医疗费用（交通、住宿、营养等 - 基于问卷相关变量）
      indirect_medical_cost = safe_numeric(
        if("t3.8" %in% names(data)) t3.8 else 0   # 交通费
      )+ safe_numeric(
        if("t3.9" %in% names(data)) t3.9 else 0   # 住宿费
      ) + safe_numeric(
        if("t3.10" %in% names(data)) t3.10 else 0  # 餐饮费
      ) + safe_numeric(
        if("t3.11" %in% names(data)) t3.11 else 0 # 营养费
      ) + safe_numeric(
        if("t3.12" %in% names(data)) t3.12 else 0  # 专职护工
      )+ safe_numeric(
        if("t3.13" %in% names(data)) t3.13 else 0  # 其他费用
      )+ safe_numeric(
        if("t3.14" %in% names(data)) t3.14 else 0  # 其他费用
      ) ,
      
      # 5.5 与t3.4对比
      t3_4_value = safe_get("t3.4"),
      difference_with_t34_total = total_medical_cost_calculated - t3_4_value,
      difference_with_t34_personal = total_personal_expenditure_calculated - t3_4_value,
      
      # 5.6 报销超限检测
      platform_reimbursement_excess = platform_additional_reimbursement - platform_additional_reimbursement_limited,
      private_reimbursement_excess = private_total_reimbursement - private_total_reimbursement_limited,
      genetic_reimbursement_excess = genetic_total_reimbursement - genetic_total_reimbursement_limited,
      
      total_reimbursement_excess = platform_reimbursement_excess + private_reimbursement_excess +
                  genetic_reimbursement_excess,
      
      # 5.7 数据质量标记
      data_quality_flag = case_when(
        abs(calculation_check) > 0.01 ~ "计算不平衡",
        total_medical_cost_calculated <= 0 ~ "无医疗费用",
        total_personal_expenditure_calculated < 0 ~ "个人自付为负",
        total_reimbursement_excess > 0.01 ~ "存在报销超限",
        TRUE ~ "计算正常"
      )
    )
  # 统计报销超限情况
  excess_summary <-   data %>%
    summarise(
      总记录数 = n(),
      医保平台报销超限记录数 = sum(platform_reimbursement_excess > 0.01, na.rm = TRUE),
      私立医院报销超限记录数 = sum(private_reimbursement_excess > 0.01, na.rm = TRUE),
    
      基因检测报销超限记录数 = sum(genetic_reimbursement_excess > 0.01, na.rm = TRUE),
      总报销超限记录数 = sum(total_reimbursement_excess > 0.01, na.rm = TRUE),
      医保平台超限总金额 = sum(platform_reimbursement_excess, na.rm = TRUE),
      私立医院超限总金额 = sum(private_reimbursement_excess, na.rm = TRUE),
      
      基因检测超限总金额 = sum(genetic_reimbursement_excess, na.rm = TRUE),
      总超限金额 = sum(total_reimbursement_excess, na.rm = TRUE)
    )
  
  print("\n=== 报销超限统计报告 ===")
  print(excess_summary)
  
  if(excess_summary$总报销超限记录数 > 0) {
    print(paste("\n⚠️ 发现", excess_summary$总报销超限记录数, "条记录存在报销超限"))
    print(paste("总超限金额:", round(excess_summary$总超限金额, 2), "万元"))
    print("已自动调整为不超过相应费用总额，确保个人支付费用非负")
  } else {
    print("\n✅ 无报销超限记录，所有个人支付费用均为非负值")
  }
  return(data)
  
}
generate_medical_expenditure_summarydat111 <- function(data) {
  
  print("=== 生成医疗费用分项汇总报告 ===")
  
  # 创建汇总表
  summary_table <- data.frame(
    费用分项 = character(),
    总费用_万元 = numeric(),
    报销费用_万元 = numeric(),
    个人自付_万元 = numeric(),
    个人自付比例_percent = numeric(),
    有效案例数 = numeric(),
    stringsAsFactors = FALSE
  )
  
  # 定义分项
  components <- list(
    "医保平台费用" = list(
      total = "platform_total_cost",
      reimbursement = "platform_total_reimbursement",
      personal = "platform_final_personal"
    ),
    "私立医院/境外医疗" = list(
      total = "private_total_cost",
      reimbursement = "private_total_reimbursement",
      personal = "private_final_personal"
    ),
   
    "基因检测费用" = list(
      total = "genetic_test_total_cost",
      reimbursement = "genetic_total_reimbursement",
      personal = "genetic_test_final_personal"
    )
  )
  
  # 计算各分项统计
  for(comp_name in names(components)) {
    comp <- components[[comp_name]]
    
    if(all(c(comp$total, comp$reimbursement, comp$personal) %in% names(data))) {
      
      # 筛选有效案例（总费用>0）
      valid_cases <- data[[comp$total]] > 0 & !is.na(data[[comp$total]])
      
      if(sum(valid_cases) > 0) {
        total_sum <- sum(data[[comp$total]][valid_cases], na.rm = TRUE)
        reimb_sum <- sum(data[[comp$reimbursement]][valid_cases], na.rm = TRUE)
        personal_sum <- sum(data[[comp$personal]][valid_cases], na.rm = TRUE)
        personal_rate <- ifelse(total_sum > 0, personal_sum / total_sum * 100, 0)
        
        summary_table <- rbind(summary_table, data.frame(
          费用分项 = comp_name,
          总费用_万元 = round(total_sum, 2),
          报销费用_万元 = round(reimb_sum, 2),
          个人自付_万元 = round(personal_sum, 2),
          个人自付比例_percent = round(personal_rate, 1),
          有效案例数 = sum(valid_cases),
          stringsAsFactors = FALSE
        ))
      }
    }
  }
  
  # 添加总计行
  if(all(c("total_medical_cost_calculated", "total_reimbursement_calculated",
           "total_personal_expenditure_calculated") %in% names(data))) {
    
    valid_total <- data$total_medical_cost_calculated > 0 & !is.na(data$total_medical_cost_calculated)
    
    if(sum(valid_total) > 0) {
      total_cost_sum <- sum(data$total_medical_cost_calculated[valid_total], na.rm = TRUE)
      total_reimb_sum <- sum(data$total_reimbursement_calculated[valid_total], na.rm = TRUE)
      total_personal_sum <- sum(data$total_personal_expenditure_calculated[valid_total], na.rm = TRUE)
      total_personal_rate <- ifelse(total_cost_sum > 0, total_personal_sum / total_cost_sum * 100, 0)
      
      summary_table <- rbind(summary_table, data.frame(
        费用分项 = "总计",
        总费用_万元 = round(total_cost_sum, 2),
        报销费用_万元 = round(total_reimb_sum, 2),
        个人自付_万元 = round(total_personal_sum, 2),
        个人自付比例_percent = round(total_personal_rate, 1),
        有效案例数 = sum(valid_total),
        stringsAsFactors = FALSE
      ))
    }
  }
  
  return(summary_table)
}

# ============================================================================
# 运行医疗费用分项计算
# ============================================================================

if(exists("dat2.1.1")) {

  print("=== 开始医疗费用分项计算 ===")

  # 应用计算函数
  dat2.1.1 <- calculate_medical_expenditure_by_components(dat2.1.1)

  # 生成汇总报告
  expenditure_summary <- generate_medical_expenditure_summary(dat2.1.1)

  # 显示汇总报告
  print("\n=== 医疗费用分项汇总报告 ===")
  print(expenditure_summary)

  # 数据质量检查
  if("data_quality_flag" %in% names(dat2.1.1)) {
    quality_table <- table(dat2.1.1$data_quality_flag)
    print("\n=== 数据质量检查 ===")
    print(quality_table)
  }

  # 与t3.4对比
  if(all(c("total_personal_expenditure_calculated", "t3_4_value") %in% names(dat2.1.1))) {
    valid_comparison <- !is.na(dat2.1.1$total_personal_expenditure_calculated) &
      !is.na(dat2.1.1$t3_4_value) &
      dat2.1.1$t3_4_value > 0

    if(sum(valid_comparison) > 0) {
      calc_mean <- mean(dat2.1.1$total_personal_expenditure_calculated[valid_comparison])
      t34_mean <- mean(dat2.1.1$t3_4_value[valid_comparison])

      print("\n=== 与t3.4对比 ===")
      cat("分项计算个人自付平均值：", round(calc_mean, 2), "万元\n")
      cat("t3.4平均值：", round(t34_mean, 2), "万元\n")
      cat("平均差异：", round(calc_mean - t34_mean, 2), "万元\n")
    }
  }

  # 保存结果
  write.csv(dat2.1.1, "医疗费用分项计算结果.csv", row.names = FALSE, fileEncoding = "UTF-8")
  write.csv(expenditure_summary, "医疗费用分项汇总表.csv", row.names = FALSE, fileEncoding = "UTF-8")

  print("\n✅ 医疗费用分项计算完成！")
  print("结果已保存到:")
  print("- 医疗费用分项计算结果.csv")
  print("- 医疗费用分项汇总表.csv")

} else {
  print("❌ dat2.1.1数据不存在")
}
 
if(exists("dat1.1.1")) {
  
  print("=== 开始医疗费用分项计算 ===")
  
  # 应用计算函数
  dat1.1.1 <- calculate_medical_expenditure_by_componentsdat111(dat1.1.1)
  
  # 生成汇总报告
  expenditure_summary <- generate_medical_expenditure_summarydat111(dat1.1.1)
  
  # 显示汇总报告
  print("\n=== 医疗费用分项汇总报告 ===")
  print(expenditure_summary)
  
  # 数据质量检查
  if("data_quality_flag" %in% names(dat1.1.1)) {
    quality_table <- table(dat1.1.1$data_quality_flag)
    print("\n=== 数据质量检查 ===")
    print(quality_table)
  }
  
  # 与t3.4对比
  if(all(c("total_personal_expenditure_calculated", "t3_4_value") %in% names(dat1.1.1))) {
    valid_comparison <- !is.na(dat1.1.1$total_personal_expenditure_calculated) &
      !is.na(dat1.1.1$t3_4_value) &
      dat1.1.1$t3_4_value > 0
    
    if(sum(valid_comparison) > 0) {
      calc_mean <- mean(dat1.1.1$total_personal_expenditure_calculated[valid_comparison])
      t34_mean <- mean(dat1.1.1$t3_4_value[valid_comparison])
      
      print("\n=== 与t3.4对比 ===")
      cat("分项计算个人自付平均值：", round(calc_mean, 2), "万元\n")
      cat("t3.4平均值：", round(t34_mean, 2), "万元\n")
      cat("平均差异：", round(calc_mean - t34_mean, 2), "万元\n")
    }
  }
  
  # 保存结果
  write.csv(dat1.1.1, "dat1.1.1医疗费用分项计算结果.csv", row.names = FALSE, fileEncoding = "UTF-8")
  write.csv(expenditure_summary, "dat1.1.1医疗费用分项汇总表.csv", row.names = FALSE, fileEncoding = "UTF-8")
  
  print("\n✅ 医疗费用分项计算完成！")
  print("结果已保存到:")
  print("- 医疗费用分项计算结果.csv")
  print("- 医疗费用分项汇总表.csv")
  
} else {
  print("❌ dat1.1.1数据不存在")
}

 
# ============================================================================
# 更新版数据异常值检测和处理（基于新的医疗费用分项计算）
# ============================================================================

# 诊断特定患者的极端值函数（更新版）
diagnose_specific_patients_updated <- function(data, patient_ids) {
  
  print("=== 诊断特定患者的医疗费用分项（更新版） ===")
  
  for(id in patient_ids) {
    cat("\n患者ID:", id, "\n")
    cat(paste(rep("-", 50), collapse = ""), "\n")
    
    # 找到该患者的数据
    patient_data <- data[data$t5.8 == id, ]
    
    if(nrow(patient_data) == 0) {
      cat("❌ 未找到该患者数据\n")
      next
    }
    
    # 显示关键费用变量（更新为完整的分项）
    cost_vars <- c("t3.4",
                   # 医保平台费用
                   "t3.5.1.1", "t3.5.1.2", "t3.5.2.1", "t3.5.2.2",
                   "t3.5.3.1", "t3.5.3.2", "t3.5.4.1", "t3.5.4.2",
                   # 额外报销
                   "t3.5.5", "t3.5.6", "t3.5.7", "t3.5.8",
                   # 私立医院/境外医疗
                   "t3.6.1.1", "t3.6.1_1", "t3.6.1.1.1", "t3.6.1.1.2", "t3.6.1.1.3", "t3.6.1.1.4",
                   # 自费药物
                   "t3.7.1.1", "t3.7.1_1", "t3.7.1.1.1", "t3.7.1.1.2", "t3.7.1.1.3", "t3.7.1.1.4",
                   # 基因检测
                   "t3.8.1.1", "t3.8.1_1", "t3.8.1.1.1", "t3.8.1.1.2", "t3.8.1.1.3", "t3.8.1.1.4")
    
    cat("原始费用数据：\n")
    for(var in cost_vars) {
      if(var %in% names(patient_data)) {
        value <- patient_data[[var]][1]
        cat("  ", var, ":", ifelse(is.na(value), "NA", value), "\n")
      }
    }
    
    # 显示计算后的分项（更新为新的变量名）
    cat("\n计算结果：\n")
    if("t3_4_value" %in% names(patient_data)) {
      cat("  总费用法(t3.4):", patient_data$t3_4_value[1], "万元\n")
    }
    if("total_personal_expenditure_calculated" %in% names(patient_data)) {
      cat("  分项计算个人自付:", patient_data$total_personal_expenditure_calculated[1], "万元\n")
    }
    if("total_medical_cost_calculated" %in% names(patient_data)) {
      cat("  分项计算总费用:", patient_data$total_medical_cost_calculated[1], "万元\n")
    }
    if("total_reimbursement_calculated" %in% names(patient_data)) {
      cat("  分项计算总报销:", patient_data$total_reimbursement_calculated[1], "万元\n")
    }
    
    # 显示各分项详情
    cat("\n分项详情：\n")
    if("platform_final_personal" %in% names(patient_data)) {
      cat("  医保平台个人自付:", patient_data$platform_final_personal[1], "万元\n")
    }
    if("private_final_personal" %in% names(patient_data)) {
      cat("  私立医院个人自付:", patient_data$private_final_personal[1], "万元\n")
    }
    if("selfpaid_drug_final_personal" %in% names(patient_data)) {
      cat("  自费药物个人自付:", patient_data$selfpaid_drug_final_personal[1], "万元\n")
    }
    if("genetic_test_final_personal" %in% names(patient_data)) {
      cat("  基因检测个人自付:", patient_data$genetic_test_final_personal[1], "万元\n")
    }
  }
}

# 综合异常值检测函数（更新版）
detect_comprehensive_outliers <- function(data) {
  
  print("=== 综合异常值检测（更新版） ===")
  
  # 检查所有医疗费用相关变量
  medical_vars <- c("t3.4",
                    # 医保平台费用
                    "t3.5.1.1", "t3.5.1.2", "t3.5.2.1", "t3.5.2.2",
                    "t3.5.3.1", "t3.5.3.2", "t3.5.4.1", "t3.5.4.2",
                    # 额外报销
                    "t3.5.5", "t3.5.6", "t3.5.7", "t3.5.8",
                    # 私立医院/境外医疗
                    "t3.6.1.1", "t3.6.1_1", "t3.6.1.1.1", "t3.6.1.1.2", "t3.6.1.1.3", "t3.6.1.1.4",
                    # 自费药物
                    "t3.7.1.1", "t3.7.1_1", "t3.7.1.1.1", "t3.7.1.1.2", "t3.7.1.1.3", "t3.7.1.1.4",
                    # 基因检测
                    "t3.8.1.1", "t3.8.1_1", "t3.8.1.1.1", "t3.8.1.1.2", "t3.8.1.1.3", "t3.8.1.1.4")
  
  outlier_summary <- data.frame(
    变量 = character(),
    异常值数量 = numeric(),
    异常值阈值 = numeric(),
    最大值 = numeric(),
    建议处理 = character(),
    stringsAsFactors = FALSE
  )
  
  for(var in medical_vars) {
    if(var %in% names(data)) {
      
      values <- as.numeric(data[[var]])
      values <- values[!is.na(values) & values > 0]
      
      if(length(values) > 0) {
        
        # 根据变量类型设置合理上限
        reasonable_max <- case_when(
          var %in% c("t3.5.1.1", "t3.5.2.1") ~ 200,  # 门诊、住院总费用
          var %in% c("t3.5.1.2", "t3.5.2.2") ~ 150,  # 门诊、住院个人支付
          var %in% c("t3.5.3.1") ~ 100,              # 药店总费用
          var %in% c("t3.5.3.2") ~ 100,               # 药店个人支付
          var %in% c("t3.5.4.1", "t3.5.4.2") ~ 100,   # 未分类费用
          var %in% c("t3.5.5", "t3.5.6", "t3.5.7", "t3.5.8") ~ 100,  # 各类报销
          var %in% c("t3.6.1.1", "t3.6.1_1") ~ 300,  # 私立医院费用
          var %in% c("t3.7.1.1", "t3.7.1_1") ~ 200,  # 自费药物费用
          var %in% c("t3.8.1.1", "t3.8.1_1") ~ 100,   # 基因检测费用
          var == "t3.4" ~ 600,                        # 总费用
          TRUE ~ 100                                  # 其他费用
        )
        
        # 统计异常值
        outliers <- values[values > reasonable_max]
        outlier_count <- length(outliers)
        max_value <- max(values)
        
        # 判断处理建议
        if(outlier_count > 0) {
          # 检查是否可能是单位错误
          potential_corrected <- outliers / 10000
          if(mean(potential_corrected <= reasonable_max) > 0.8) {
            suggestion <- "单位转换（元→万元）"
          } else {
            suggestion <- "设为中位数"
          }
        } else {
          suggestion <- "无需处理"
        }
        
        outlier_summary <- rbind(outlier_summary, data.frame(
          变量 = var,
          异常值数量 = outlier_count,
          异常值阈值 = reasonable_max,
          最大值 = round(max_value, 2),
          建议处理 = suggestion,
          stringsAsFactors = FALSE
        ))
        
        # 显示详细信息
        if(outlier_count > 0) {
          cat("\n变量:", var, "\n")
          cat("  总观测数:", length(values), "\n")
          cat("  最大值:", round(max_value, 2), "万元\n")
          cat("  异常值数量:", outlier_count, "例（>", reasonable_max, "万元）\n")
          
          if(outlier_count <= 10) {
            cat("  异常值列表:", paste(round(outliers, 2), collapse = ", "), "\n")
            
            # 显示对应的患者ID
            outlier_indices <- which(as.numeric(data[[var]]) > reasonable_max)
            if("t5.8" %in% names(data) && length(outlier_indices) <= 10) {
              outlier_ids <- data$t5.8[outlier_indices]
              cat("  对应患者ID:", paste(outlier_ids, collapse = ", "), "\n")
            }
          }
        }
      }
    }
  }
  
  print(outlier_summary)
  return(outlier_summary)
}

# 基于一致性的智能单位检查和修正函数（重点检查元/万元单位）
fix_outliers_based_on_consistency <- function(data) {
  
  print("=== 基于一致性的智能单位检查和修正 ===")
  
  # 创建详细修正记录表
  correction_log <- data.frame(
    患者ID = character(),
    变量名 = character(),
    变量描述 = character(),
    修正前数值 = numeric(),
    修正后数值 = numeric(),
    修正类型 = character(),
    修正理由 = character(),
    t3_4原值 = numeric(),
    分项计算原值 = numeric(),
    相对差异_修正前 = numeric(),
    相对差异_修正后 = numeric(),
    修正时间 = character(),
    stringsAsFactors = FALSE
  )
  
  # 首先计算医疗费用分项（如果还没有计算）
  if(!"total_personal_expenditure_calculated" %in% names(data)) {
    print("先计算医疗费用分项...")
    if(exists("calculate_medical_expenditure_by_components")) {
      data <- calculate_medical_expenditure_by_components(data)
    } else {
      print("❌ 医疗费用分项计算函数不存在")
      return(list(data = data, correction_log = correction_log))
    }
  }
  
  # 安全数值转换
  safe_numeric <- function(x) {
    suppressWarnings(as.numeric(as.character(x)))
  }
  
  # 计算一致性指标
  data <- data %>%
    mutate(
      t3_4_value_safe = safe_numeric(t3.4),
      calculated_personal_safe = safe_numeric(total_personal_expenditure_calculated),
      
      # 计算差异和一致性
      difference_abs = abs(calculated_personal_safe - t3_4_value_safe),
      difference_relative = ifelse(t3_4_value_safe > 0,
                                   difference_abs / t3_4_value_safe * 100,
                                   NA),
      
      # 一致性判断标准（更严格的标准）
      consistency_status = case_when(
        is.na(t3_4_value_safe) | is.na(calculated_personal_safe) ~ "数据缺失",
        t3_4_value_safe <= 0 & calculated_personal_safe <= 0 ~ "均为零值",
        difference_relative <= 30 ~ "高度一致",
        difference_relative <= 80 ~ "基本一致",
        difference_relative <= 200 ~ "存在差异",
        TRUE ~ "严重不一致"
      )
    )
  
  # 统计一致性情况
  print("\n=== 一致性分析结果 ===")
  consistency_table <- table(data$consistency_status)
  print(consistency_table)
  
  # 识别需要处理的不一致案例（包括"存在差异"和"严重不一致"）
  inconsistent_cases <- data %>%
    filter(consistency_status %in% c("存在差异", "严重不一致")) %>%
    select(t5.8, t3_4_value_safe, calculated_personal_safe, difference_relative, consistency_status) %>%
    arrange(desc(difference_relative))
  
  if(nrow(inconsistent_cases) > 0) {
    print(paste("\n发现", nrow(inconsistent_cases), "例不一致的案例，重点检查单位问题"))
    
    # 医疗费用变量分类（重点关注可能的单位错误）
    medical_vars_info <- list(
      # 医保平台费用（最常见单位错误）
      "t3.5.1.1" = list(limit = 200, desc = "门诊总费用"),
      "t3.5.1.2" = list(limit = 200, desc = "门诊个人支付"),
      "t3.5.2.1" = list(limit = 600, desc = "住院总费用"),
      "t3.5.2.2" = list(limit = 600, desc = "住院个人支付"),
      "t3.5.3.1" = list(limit = 200, desc = "药店总费用"),
      "t3.5.3.2" = list(limit = 200, desc = "药店个人支付"),
      "t3.5.4.1" = list(limit = 300, desc = "未分类总费用"),
      "t3.5.4.2" = list(limit = 300, desc = "未分类个人支付"),
      
      # 报销费用
      "t3.5.5" = list(limit = 200, desc = "公费医疗报销"),
      "t3.5.6" = list(limit = 200, desc = "商业补充医疗保险"),
      "t3.5.7" = list(limit = 200, desc = "商业保险报销"),
      "t3.5.8" = list(limit = 200, desc = "其他途径报销"),
      
      # 私立医院费用
      "t3.6.1.1" = list(limit = 300, desc = "私立医院总费用"),
      "t3.6.1_1" = list(limit = 300, desc = "私立医院费用（不可报销）"),
      "t3.6.1.1.1" = list(limit = 200, desc = "私立医院公费报销"),
      "t3.6.1.1.2" = list(limit = 200, desc = "私立医院商业补充报销"),
      "t3.6.1.1.3" = list(limit = 200, desc = "私立医院商业保险报销"),
      "t3.6.1.1.4" = list(limit = 200, desc = "私立医院其他报销"),
      
      # 自费药物费用
      "t3.7.1.1" = list(limit = 200, desc = "自费药物总费用"),
      "t3.7.1_1" = list(limit = 200, desc = "自费药物费用（不可报销）"),
      "t3.7.1.1.1" = list(limit = 200, desc = "自费药物公费报销"),
      "t3.7.1.1.2" = list(limit = 200, desc = "自费药物商业补充报销"),
      "t3.7.1.1.3" = list(limit = 200, desc = "自费药物商业保险报销"),
      "t3.7.1.1.4" = list(limit = 200, desc = "自费药物其他报销"),
      
      # 基因检测费用
      "t3.8.1.1" = list(limit = 100, desc = "基因检测总费用"),
      "t3.8.1_1" = list(limit = 100, desc = "基因检测费用（不可报销）"),
      "t3.8.1.1.1" = list(limit = 100, desc = "基因检测公费报销"),
      "t3.8.1.1.2" = list(limit = 100, desc = "基因检测商业补充报销"),
      "t3.8.1.1.3" = list(limit = 100, desc = "基因检测商业保险报销"),
      "t3.8.1.1.4" = list(limit = 100, desc = "基因检测其他报销"),
      
      # 总费用
      "t3.4" = list(limit = 500, desc = "个人医疗总支出")
    )
    
    total_corrections <- 0
    corrected_patients <- character()
    unit_conversion_count <- 0
    
    # 对每个不一致案例进行详细检查
    for(i in 1:nrow(inconsistent_cases)) {
      patient_id <- inconsistent_cases$t5.8[i]
      patient_idx <- which(data$t5.8 == patient_id)
      
      cat("\n" , paste(rep("=", 60), collapse = ""), "\n")
      cat("检查患者", patient_id, "（", inconsistent_cases$consistency_status[i], "）:\n")
      cat("  t3.4（总支出）:", inconsistent_cases$t3_4_value_safe[i], "万元\n")
      cat("  分项计算个人自付:", inconsistent_cases$calculated_personal_safe[i], "万元\n")
      cat("  相对差异:", round(inconsistent_cases$difference_relative[i], 1), "%\n")
      
      patient_corrected <- FALSE
      patient_unit_conversions <- 0
      
      # 记录修正前的分项计算值
      original_calculated <- inconsistent_cases$calculated_personal_safe[i]
      
      # 检查该患者的各个医疗费用变量
      for(var in names(medical_vars_info)) {
        var_info <- medical_vars_info[[var]]
        limit <- var_info$limit
        desc <- var_info$desc
        
        if(var %in% names(data) && length(patient_idx) > 0) {
          original_value <- safe_numeric(data[[var]][patient_idx])
          
          if(!is.na(original_value) && original_value > 0) {
            
            # 检查是否超过合理上限（可能的单位错误）
            if(original_value > limit) {
              
              # 计算单位转换后的值
              converted_value <- original_value / 10000
              
              # 判断单位转换的合理性
              if(converted_value > 0 && converted_value <= limit) {
                # 单位转换后合理，应用转换
                data[[var]][patient_idx] <- converted_value
                cat("    ✓ 修正", var, "(", desc, "):",
                    original_value, "万元 → ", round(converted_value, 4), "万元（元→万元转换）\n")
                
                # 记录修正详情
                correction_log <- rbind(correction_log, data.frame(
                  患者ID = patient_id,
                  变量名 = var,
                  变量描述 = desc,
                  修正前数值 = original_value,
                  修正后数值 = round(converted_value, 4),
                  修正类型 = "单位转换",
                  修正理由 = paste0("原值", original_value, "万元超出合理上限", limit, "万元，疑似单位错误（元填成万元），转换后", round(converted_value, 4), "万元在合理范围内"),
                  t3_4原值 = inconsistent_cases$t3_4_value_safe[i],
                  分项计算原值 = original_calculated,
                  相对差异_修正前 = round(inconsistent_cases$difference_relative[i], 1),
                  相对差异_修正后 = NA,  # 稍后重新计算
                  修正时间 = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
                  stringsAsFactors = FALSE
                ))
                
                total_corrections <- total_corrections + 1
                unit_conversion_count <- unit_conversion_count + 1
                patient_corrected <- TRUE
                patient_unit_conversions <- patient_unit_conversions + 1
                
              } else if(original_value > limit * 100) {
                # 极端异常值，设为中位数
                median_value <- median(safe_numeric(data[[var]]), na.rm = TRUE)
                if(is.na(median_value) || median_value > limit) {
                  median_value <- limit / 10  # 设为较小的合理值
                }
                data[[var]][patient_idx] <- median_value
                cat("    ⚠ 修正", var, "(", desc, "):",
                    original_value, "万元 → ", round(median_value, 4), "万元（极端值→中位数）\n")
                
                # 记录修正详情
                correction_log <- rbind(correction_log, data.frame(
                  患者ID = patient_id,
                  变量名 = var,
                  变量描述 = desc,
                  修正前数值 = original_value,
                  修正后数值 = round(median_value, 4),
                  修正类型 = "极端值处理",
                  修正理由 = paste0("原值", original_value, "万元为极端异常值（超出", limit, "万元上限", round(original_value/limit, 1), "倍），设为中位数", round(median_value, 4), "万元"),
                  t3_4原值 = inconsistent_cases$t3_4_value_safe[i],
                  分项计算原值 = original_calculated,
                  相对差异_修正前 = round(inconsistent_cases$difference_relative[i], 1),
                  相对差异_修正后 = NA,  # 稍后重新计算
                  修正时间 = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
                  stringsAsFactors = FALSE
                ))
                
                total_corrections <- total_corrections + 1
                patient_corrected <- TRUE
                
              } else {
                # 轻微超出，但可能合理，记录但不修改
                cat("    ℹ 注意", var, "(", desc, "):",
                    original_value, "万元（超出", limit, "万元上限，但保留原值）\n")
              }
            }
          }
        }
      }
      
      if(patient_corrected) {
        corrected_patients <- c(corrected_patients, patient_id)
        cat("  → 该患者共修正", patient_unit_conversions, "个变量\n")
      } else {
        cat("  → 该患者无需修正单项数值\n")
      }
    }
    
    cat("\n", paste(rep("=", 60), collapse = ""), "\n")
    cat("=== 修正汇总 ===\n")
    cat("总计修正", total_corrections, "个异常值\n")
    cat("其中单位转换（元→万元）:", unit_conversion_count, "个\n")
    cat("涉及患者", length(corrected_patients), "例\n")
    if(length(corrected_patients) > 0) {
      cat("修正患者ID:", paste(corrected_patients, collapse = ", "), "\n")
    }
    
    # 如果有修正记录，重新计算修正后的相对差异
    if(nrow(correction_log) > 0) {
      print("\n=== 重新计算修正后的一致性 ===")
      
      # 重新计算医疗费用分项（基于修正后的数据）
      if(exists("calculate_medical_expenditure_by_components")) {
        data_temp <- calculate_medical_expenditure_by_components(data)
        
        # 更新修正记录中的修正后相对差异
        for(j in 1:nrow(correction_log)) {
          patient_id_log <- correction_log$患者ID[j]
          patient_idx_log <- which(data_temp$t5.8 == patient_id_log)
          
          if(length(patient_idx_log) > 0) {
            t3_4_val <- safe_numeric(data_temp$t3.4[patient_idx_log])
            calc_val <- safe_numeric(data_temp$total_personal_expenditure_calculated[patient_idx_log])
            
            if(!is.na(t3_4_val) && !is.na(calc_val) && t3_4_val > 0) {
              new_diff <- abs(calc_val - t3_4_val) / t3_4_val * 100
              correction_log$相对差异_修正后[j] <- round(new_diff, 1)
            }
          }
        }
        
        data <- data_temp  # 使用重新计算后的数据
      }
      
      # 导出修正记录
      if(  data$t0.5[1] =='1' ){
        
        write.csv(correction_log, "dat1.1.1医疗费用异常值修正记录.csv", row.names = FALSE, fileEncoding = "UTF-8")
        cat("\n✅ 修正记录已导出到: dat1.1.1医疗费用异常值修正记录.csv\n")
      } else if( data$t0.5[1]=='2' ){
        
        write.csv(correction_log, "dat2.1.1医疗费用异常值修正记录.csv", row.names = FALSE, fileEncoding = "UTF-8")
        cat("\n✅ 修正记录已导出到: dat2.1.1医疗费用异常值修正记录.csv\n")
      }

      
      # 显示修正记录摘要
      print("\n=== 修正记录摘要 ===")
      print(correction_log[, c("患者ID", "变量名", "变量描述", "修正前数值", "修正后数值", "修正类型")])
    }
    
  } else {
    print("\n✅ 未发现不一致的案例，无需修正单项异常值")
  }
  
  # 返回修正后的数据和修正记录
  return(list(
    data = data,
    correction_log = correction_log
  ))
}

# 特定患者列表
extreme_patients <- data.frame(
  t5.8 = c("005301", "006570", "005310", "008937", "005871",
           "005077", "007784", "004809", "006944", "008818"),
  stringsAsFactors = FALSE
)

# ###第二期已受治疗 运行基于一致性的智能异常值处理
if(exists("dat2.1.1")) {
  
  print("=== 开始基于一致性的智能异常值处理流程 ===")
  
  # 1. 先计算医疗费用分项（如果还没有计算）
  if(!"total_personal_expenditure_calculated" %in% names(dat2.1.1)) {
    if(exists("calculate_medical_expenditure_by_components")) {
      print("=== 计算医疗费用分项 ===")
      dat2.1.1 <- calculate_medical_expenditure_by_components(dat2.1.1)
    } else {
      print("❌ 医疗费用分项计算函数不存在")
    }
  }
  
  # 2. 诊断特定患者的原始数据
  diagnose_specific_patients_updated(dat2.1.1, extreme_patients$t5.8)
  
  # 3. 基于一致性的智能异常值处理
  correction_result <- fix_outliers_based_on_consistency(dat2.1.1)
  dat2.1.1 <- correction_result$data
  correction_log <- correction_result$correction_log
  
  # 4. 重新计算医疗费用（修正后）
  if(exists("calculate_medical_expenditure_by_components")) {
    print("\n=== 重新计算医疗费用（修正后） ===")
    dat2.1.1 <- calculate_medical_expenditure_by_components(dat2.1.1)
  }
  
  # 5. 一致性改善效果检查
  print("\n=== 一致性改善效果检查 ===")
  
  if(all(c("total_personal_expenditure_calculated", "t3.4") %in% names(dat2.1.1))) {
    
    # 重新计算一致性指标
    safe_numeric <- function(x) suppressWarnings(as.numeric(as.character(x)))
    
    t3_4_values <- safe_numeric(dat2.1.1$t3.4)
    calculated_values <- safe_numeric(dat2.1.1$total_personal_expenditure_calculated)
    
    # 有效对比案例
    valid_cases <- !is.na(t3_4_values) & !is.na(calculated_values) &
      t3_4_values > 0 & calculated_values > 0
    
    if(sum(valid_cases) > 0) {
      differences <- abs(calculated_values[valid_cases] - t3_4_values[valid_cases])
      relative_differences <- differences / t3_4_values[valid_cases] * 100
      
      cat("有效对比案例数：", sum(valid_cases), "\n")
      cat("平均绝对差异：", round(mean(differences), 2), "万元\n")
      cat("平均相对差异：", round(mean(relative_differences), 1), "%\n")
      cat("中位数相对差异：", round(median(relative_differences), 1), "%\n")
      
      # 一致性分布
      consistency_levels <- cut(relative_differences,
                                breaks = c(0, 20, 50, 100, Inf),
                                labels = c("高度一致(≤20%)", "基本一致(20-50%)",
                                           "存在差异(50-100%)", "严重不一致(>100%)"),
                                include.lowest = TRUE)
      
      consistency_table <- table(consistency_levels)
      print("\n一致性分布：")
      print(consistency_table)
      print(round(prop.table(consistency_table) * 100, 1))
    }
    
    # 显示修正后的统计
    max_calculated <- max(calculated_values, na.rm = TRUE)
    max_t34 <- max(t3_4_values, na.rm = TRUE)
    
    cat("\n修正后统计：\n")
    cat("分项计算最大值：", round(max_calculated, 2), "万元\n")
    cat("t3.4最大值：", round(max_t34, 2), "万元\n")
    
    extreme_calculated <- sum(calculated_values > 100, na.rm = TRUE)
    extreme_t34 <- sum(t3_4_values > 100, na.rm = TRUE)
    
    cat("分项计算>100万元：", extreme_calculated, "例\n")
    cat("t3.4>100万元：", extreme_t34, "例\n")
  }
  
  # 6. 检查特定患者的修正效果
  print("\n=== 特定患者修正效果 ===")
  for(id in extreme_patients$t5.8) {
    patient_data <- dat2.1.1[dat2.1.1$t5.8 == id, ]
    if(nrow(patient_data) > 0) {
      if(all(c("total_personal_expenditure_calculated", "t3.4") %in% names(patient_data))) {
        t34_val <- safe_numeric(patient_data$t3.4[1])
        calc_val <- safe_numeric(patient_data$total_personal_expenditure_calculated[1])
        
        if(!is.na(t34_val) && !is.na(calc_val) && t34_val > 0) {
          diff_pct <- abs(calc_val - t34_val) / t34_val * 100
          cat("患者", id, ": 分项计算=", round(calc_val, 2),
              "万元, t3.4=", round(t34_val, 2),
              "万元, 差异=", round(diff_pct, 1), "%\n")
        } else {
          cat("患者", id, ": 数据缺失或异常\n")
        }
      }
    }
  }
  
  # 7. 保存修正后的数据
  write.csv(dat2.1.1, "基于一致性修正后数据.csv", row.names = FALSE, fileEncoding = "UTF-8")
  cat("\n✅ 数据已保存到: 基于一致性修正后数据.csv\n")
  
} else {
  print("❌ dat2.1.1数据不存在")
}

###第二期未接受治疗 
if(exists("dat1.1.1")) {
  
  print("=== 开始基于一致性的智能异常值处理流程 ===")
 
  # 1. 先计算医疗费用分项（如果还没有计算）
  if(!"total_personal_expenditure_calculated" %in% names(dat1.1.1)) {
    if(exists("calculate_medical_expenditure_by_componentsdat111")) {
      print("=== 计算医疗费用分项 ===")
      dat1.1.1 <- calculate_medical_expenditure_by_componentsdat111(dat1.1.1)
    } else {
      print("❌ 医疗费用分项计算函数不存在")
    }
  }
 
  # 3. 基于一致性的智能异常值处理
  correction_result <- fix_outliers_based_on_consistency(dat1.1.1)
  dat1.1.1 <- correction_result$data
  correction_log <- correction_result$correction_log
  
  # 4. 重新计算医疗费用（修正后）
  if(exists("calculate_medical_expenditure_by_componentsdat111")) {
    print("\n=== 重新计算医疗费用（修正后） ===")
    dat1.1.1 <- calculate_medical_expenditure_by_componentsdat111(dat1.1.1)
  }
  
  # 5. 一致性改善效果检查
  print("\n=== 一致性改善效果检查 ===")
  
  if(all(c("total_personal_expenditure_calculated", "t3.4") %in% names(dat1.1.1))) {
    
    # 重新计算一致性指标
    safe_numeric <- function(x) suppressWarnings(as.numeric(as.character(x)))
    
    t3_4_values <- safe_numeric(dat1.1.1$t3.4)
    calculated_values <- safe_numeric(dat1.1.1$total_personal_expenditure_calculated)
    
    # 有效对比案例
    valid_cases <- !is.na(t3_4_values) & !is.na(calculated_values) &
      t3_4_values > 0 & calculated_values > 0
    
    if(sum(valid_cases) > 0) {
      differences <- abs(calculated_values[valid_cases] - t3_4_values[valid_cases])
      relative_differences <- differences / t3_4_values[valid_cases] * 100
      
      cat("有效对比案例数：", sum(valid_cases), "\n")
      cat("平均绝对差异：", round(mean(differences), 2), "万元\n")
      cat("平均相对差异：", round(mean(relative_differences), 1), "%\n")
      cat("中位数相对差异：", round(median(relative_differences), 1), "%\n")
      
      # 一致性分布
      consistency_levels <- cut(relative_differences,
                                breaks = c(0, 20, 50, 100, Inf),
                                labels = c("高度一致(≤20%)", "基本一致(20-50%)",
                                           "存在差异(50-100%)", "严重不一致(>100%)"),
                                include.lowest = TRUE)
      
      consistency_table <- table(consistency_levels)
      print("\n一致性分布：")
      print(consistency_table)
      print(round(prop.table(consistency_table) * 100, 1))
    }
    
    # 显示修正后的统计
    max_calculated <- max(calculated_values, na.rm = TRUE)
    max_t34 <- max(t3_4_values, na.rm = TRUE)
    
    cat("\n修正后统计：\n")
    cat("分项计算最大值：", round(max_calculated, 2), "万元\n")
    cat("t3.4最大值：", round(max_t34, 2), "万元\n")
    
    extreme_calculated <- sum(calculated_values > 100, na.rm = TRUE)
    extreme_t34 <- sum(t3_4_values > 100, na.rm = TRUE)
    
    cat("分项计算>100万元：", extreme_calculated, "例\n")
    cat("t3.4>100万元：", extreme_t34, "例\n")
  }
  
  # 6. 检查特定患者的修正效果
  print("\n=== 特定患者修正效果 ===")
  for(id in extreme_patients$t5.8) {
    patient_data <- dat1.1.1[dat1.1.1$t5.8 == id, ]
    if(nrow(patient_data) > 0) {
      if(all(c("total_personal_expenditure_calculated", "t3.4") %in% names(patient_data))) {
        t34_val <- safe_numeric(patient_data$t3.4[1])
        calc_val <- safe_numeric(patient_data$total_personal_expenditure_calculated[1])
        
        if(!is.na(t34_val) && !is.na(calc_val) && t34_val > 0) {
          diff_pct <- abs(calc_val - t34_val) / t34_val * 100
          cat("患者", id, ": 分项计算=", round(calc_val, 2),
              "万元, t3.4=", round(t34_val, 2),
              "万元, 差异=", round(diff_pct, 1), "%\n")
        } else {
          cat("患者", id, ": 数据缺失或异常\n")
        }
      }
    }
  }
  
  # 7. 保存修正后的数据
  write.csv(dat1.1.1, "dat1.1.1基于一致性修正后数据.csv", row.names = FALSE, fileEncoding = "UTF-8")
  cat("\n✅ 数据已保存到: dat1.1.1基于一致性修正后数据.csv\n")
  
} else {
  print("❌ dat1.1.1数据不存在")
}
 
###  门诊费用异常值手动处理，与t3.4进行比较 单项相距巨大 ， 除以10000  
# 门诊总费用调整
dat2.1.1$t3.5.1.1[dat2.1.1$t5.8== '005586' ] <- dat2.1.1$t3.5.1.1[dat2.1.1$t5.8=='005586']/10000
dat2.1.1$t3.5.1.1[dat2.1.1$t5.8== '004954' ] <- dat2.1.1$t3.5.1.1[dat2.1.1$t5.8=='004954']/10000
dat2.1.1$t3.5.1.1[dat2.1.1$t5.8== '010180' ] <- dat2.1.1$t3.5.1.1[dat2.1.1$t5.8=='010180']/10000

# 门诊总费用调整
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '005586' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='005586']/10000
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '004954' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='004954']/10000
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '010180' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='010180']/10000

## 门诊单项费用不符合规则：总费用大于个人自付费用
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '011283' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='011283']/10000
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '007006' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='007006']/10000
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '005231' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='005231']/10000
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '005201' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='005201']/10000
dat2.1.1$t3.5.1.2[dat2.1.1$t5.8== '005777' ] <- dat2.1.1$t3.5.1.2[dat2.1.1$t5.8=='005777']/10000
 

### 药店费用异常值手动处理，与t3.4进行比较 单项相距巨大 除以10000  
dat2.1.1$t3.5.3.1[dat2.1.1$t5.8== '004679' ] <- dat2.1.1$t3.5.3.1[dat2.1.1$t5.8=='004679']/10000
dat2.1.1$t3.5.3.1[dat2.1.1$t5.8== '004954' ] <- dat2.1.1$t3.5.3.1[dat2.1.1$t5.8=='004954']/10000

dat2.1.1$t3.5.3.2[dat2.1.1$t5.8== '004679' ] <- dat2.1.1$t3.5.3.2[dat2.1.1$t5.8=='004679']/10000
dat2.1.1$t3.5.3.2[dat2.1.1$t5.8== '004954' ] <- dat2.1.1$t3.5.3.2[dat2.1.1$t5.8=='004954']/10000

## 未分类  规则：总费用大于个人自付费用
dat2.1.1$t3.5.4.2[dat2.1.1$t5.8== '005301' ] <- dat2.1.1$t3.5.4.2[dat2.1.1$t5.8=='005301']/10000
dat2.1.1$t3.5.4.2[dat2.1.1$t5.8== '008072' ] <- dat2.1.1$t3.5.4.2[dat2.1.1$t5.8=='008072']/10000

## 检查  出现 总费用是0， 但是个人自付费用不是0。  处理：将个人自付费用赋值给总费用
dat2.1.1 %>%   filter( t3.5.1.1<t3.5.1.2) %>% select(t3.5.1.1, t3.5.1.2 , t5.8)
dat2.1.1 %>%   filter( t3.5.2.1<t3.5.2.2) %>% select(t3.5.2.1, t3.5.2.2 , t5.8)
dat2.1.1 %>%   filter( t3.5.3.1<t3.5.3.2) %>% select(t3.5.3.1, t3.5.3.2 , t5.8)
dat2.1.1 %>%   filter( t3.5.4.1<t3.5.4.2) %>% select(t3.5.4.1, t3.5.4.2 , t5.8)

dat2.1.1 <-  dat2.1.1 %>% 
  mutate(t3.5.1.1 = case_when(
    t3.5.1.1 < t3.5.1.2 & t3.5.1.1 ==0 ~ t3.5.1.2,
    TRUE ~ t3.5.1.1
  ) ,  
  t3.5.2.1 = case_when(
    t3.5.2.1 < t3.5.2.2 & t3.5.2.1 ==0 ~ t3.5.2.2,
    TRUE ~ t3.5.2.1
  ) , 
  t3.5.3.1 = case_when( 
    t3.5.3.1 < t3.5.3.2 & t3.5.3.1 ==0 ~ t3.5.3.2,
    TRUE ~ t3.5.3.1
  ) , 
  t3.5.4.1 = case_when(
    t3.5.4.1 < t3.5.4.2 & t3.5.4.1 ==0 ~ t3.5.4.2,
    TRUE ~ t3.5.4.1
  )  )

## 第一期问卷手动修改


dat3.1.1 <-  dat3.1.1 %>%
  mutate( 
    t4.2.1= case_when(
      !is.na(t4.2.1 ) & t4.2.1 >=100 ~  t4.2.1/10000,
      t4.2.1==0 & t4.2.3>0 ~t4.2.3,
      
      TRUE ~ t4.2.1
    ),
    
    t4.2.3= case_when(
      !is.na(t4.2.3 ) & t4.2.3 >=100 ~  t4.2.3/10000,
      TRUE ~ t4.2.3
    ) ,
    
    t4.3.1= case_when(
      t4.3.1==0 & t4.3.3 >0 ~ t4.3.3,
      TRUE ~ t4.3.1
    )
    
  )


# 结果分析 --------------------------------------------------------------------

print("=== 变量定义和分组 ===")

create_region_groups <- function(data, region_col) {
 
  data <-  data %>%
    mutate(
      region_group = case_when(
        # 提取#号前的省份信息并匹配
        grepl("^(北京|上海|天津|江苏|浙江|广东|福建|山东)", !!sym(region_col)) ~ "东部地区",
        grepl("^(河南|湖北|湖南|安徽|江西|山西|河北|黑龙江|吉林|辽宁)", !!sym(region_col)) ~ "中部地区",
        grepl("^(四川|重庆|云南|贵州|西藏|陕西|甘肃|青海|宁夏|新疆|内蒙古|广西)", !!sym(region_col)) ~ "西部地区",
        is.na(!!sym(region_col)) ~ "未知",
        TRUE ~ "其他"
      )
    )
 
}


# 2.2 经济水平分组函数
create_income_groups <- function(data, income_col) {
  data %>%
    mutate(
      income_group = case_when(
        !!sym(income_col) <= quantile(!!sym(income_col), 0.33, na.rm = TRUE) ~ "低收入家庭",
        !!sym(income_col) <= quantile(!!sym(income_col), 0.67, na.rm = TRUE) ~ "中等收入家庭",
        TRUE ~ "高收入家庭"
      )
    )
}

 
# 2.3 医疗保险类型分组
create_insurance_groups <- function(data) {
  data %>%
    mutate(
      # 基本医保类型（多选题处理）
      has_resident_insurance = ifelse(!is.na(t3.1.0.1) & t3.1.0.1 == 1, 1, 0),
      has_employee_insurance = ifelse(!is.na(t3.1.0.2) & t3.1.0.2 == 1, 1, 0),
      has_public_insurance = ifelse(!is.na(t3.1.0.4) & t3.1.0.4 == 1, 1, 0),
      has_commercial_insurance = ifelse(!is.na(t3.1.0.6) & t3.1.0.6 == 1, 1, 0),
      
      # 基本医保覆盖情况（可能同时有多种）
      basic_insurance_count = has_resident_insurance + has_employee_insurance + has_public_insurance,
      
      # 主要基本医保类型（按优先级）
      primary_basic_insurance = case_when(
        has_public_insurance == 1 ~ "公费医疗",
        has_employee_insurance == 1 ~ "职工医保", 
        has_resident_insurance == 1 ~ "城乡居民医保",
        TRUE ~ "无基本医保"
      ),
      
      # 保险覆盖组合类型
      insurance_combination = case_when(
        basic_insurance_count == 0 & has_commercial_insurance == 0 ~ "无保险",
        basic_insurance_count == 0 & has_commercial_insurance == 1 ~ "仅商业保险",
        basic_insurance_count == 1 & has_commercial_insurance == 0 ~ "仅基本医保",
        basic_insurance_count == 1 & has_commercial_insurance == 1 ~ "基本医保+商业保险",
        basic_insurance_count > 1 & has_commercial_insurance == 0 ~ "多重基本医保",
        basic_insurance_count > 1 & has_commercial_insurance == 1 ~ "多重基本医保+商业保险",
        TRUE ~ "其他"
      ),
      
      # 保险保障水平
      insurance_level = case_when(
        basic_insurance_count == 0 & has_commercial_insurance == 0 ~ "无保障",
        basic_insurance_count >= 1 & has_commercial_insurance == 0 ~ "基础保障",
        basic_insurance_count >= 1 & has_commercial_insurance == 1 ~ "充分保障",
        basic_insurance_count == 0 & has_commercial_insurance == 1 ~ "商业保障",
        TRUE ~ "其他"
      )
    )
}
 

create_treatment_complexity <- function(data) {
  data %>%
    mutate(
      # 各种治疗方式（0/1变量）
      has_surgery = ifelse(!is.na(t2.5.1) & t2.5.1 == 1, 1, 0),           # 手术
      has_radiotherapy = ifelse(!is.na(t2.5.2) & t2.5.2 == 1, 1, 0),      # 放疗（除质子治疗外）
      has_proton = ifelse(!is.na(t2.5.3) & t2.5.3 == 1, 1, 0),            # 质子治疗
      has_chemotherapy = ifelse(!is.na(t2.5.4) & t2.5.4 == 1, 1, 0),      # 化疗
      has_targeted = ifelse(!is.na(t2.5.5) & t2.5.5 == 1, 1, 0),          # 靶向治疗
      has_immunotherapy = ifelse(!is.na(t2.5.6) & t2.5.6 == 1, 1, 0),     # 免疫治疗（CAR-T）
      has_transplant = ifelse(!is.na(t2.5.7) & t2.5.7 == 1, 1, 0),        # 器官移植（含骨髓）
      
      # 治疗复杂度分级
      treatment_complexity = case_when(
        # 高复杂度治疗：器官移植、质子治疗、免疫治疗
        (has_transplant == 1 | has_proton == 1 | has_immunotherapy == 1) ~ "高复杂度治疗",
        # 中等复杂度治疗：手术、放疗、靶向治疗
        (has_surgery == 1 | has_radiotherapy == 1 | has_targeted == 1) ~ "中等复杂度治疗",
        # 基础治疗：化疗
        has_chemotherapy == 1 ~ "基础治疗",
        TRUE ~ "其他"
      ),
      
      # 治疗方式数量
      treatment_count = has_surgery + has_radiotherapy + has_proton + has_chemotherapy + 
        has_targeted + has_immunotherapy + has_transplant,
      
      # 治疗强度分级
      treatment_intensity = case_when(
        treatment_count == 0 ~ "无治疗记录",
        treatment_count == 1 ~ "单一治疗",
        treatment_count == 2 ~ "双重治疗",
        treatment_count >= 3 ~ "多重治疗",
        TRUE ~ "其他"
      ),
      
      # 治疗类型组合
      treatment_combination = case_when(
        has_transplant == 1 ~ "包含移植治疗",
        has_proton == 1 | has_immunotherapy == 1 ~ "包含先进治疗",
        has_surgery == 1 & has_chemotherapy == 1 ~ "手术+化疗",
        has_radiotherapy == 1 & has_chemotherapy == 1 ~ "放疗+化疗",
        has_targeted == 1 & has_chemotherapy == 1 ~ "靶向+化疗",
        has_chemotherapy == 1 ~ "仅化疗",
        has_surgery == 1 ~ "仅手术",
        TRUE ~ "其他组合"
      )
    )
}
 
print("=== 变量定义和分组 ===")

### 公费医疗、 商业保险 、其他途径

# ============================================================================
# 修正商业保险参与情况
# ============================================================================

correct_commercial_insurance <- function(data) {
  
  print("=== 修正商业保险参与情况 ===")
  
  # 安全数值转换函数
  safe_numeric <- function(x) {
    result <- suppressWarnings(as.numeric(as.character(x)))
    result[is.na(result)] <- 0
    return(result)
  }
  
  data <- data %>%
    mutate(
      # ========================================================================
      # 修正t3.1.0.4（公费医疗）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_5_amount = safe_numeric(if("t3.5.5" %in% names(data)) t3.5.5 else 0),
      t3_6_1_1_1_amount = safe_numeric(if("t3.6.1.1.1" %in% names(data)) t3.6.1.1.1 else 0),
      t3_7_1_1_1_amount = safe_numeric(if("t3.7.1.1.1" %in% names(data)) t3.7.1.1.1 else 0),
      t3_8_1_1_1_amount = safe_numeric(if("t3.8.1.1.1" %in% names(data)) t3.8.1.1.1 else 0),
      
      # 判断是否有商业补充医疗保险报销
      has_commercial_supplement_reimbursement = (
        t3_5_5_amount > 0 | 
          t3_6_1_1_1_amount > 0 | 
          t3_7_1_1_1_amount > 0 | 
          t3_8_1_1_1_amount > 0
      ),
      
      # 修正t3.1.0.4
      t3.1.0.4_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_supplement_reimbursement & 
          (is.na(t3.1.0.4) | t3.1.0.4 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.4)
      ),
      
      
      # ========================================================================
      # 修正t3.1.0.5（商业补充医疗保险参与情况）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_6_amount = safe_numeric(if("t3.5.6" %in% names(data)) t3.5.6 else 0),
      t3_6_1_1_2_amount = safe_numeric(if("t3.6.1.1.2" %in% names(data)) t3.6.1.1.2 else 0),
      t3_7_1_1_2_amount = safe_numeric(if("t3.7.1.1.2" %in% names(data)) t3.7.1.1.2 else 0),
      t3_8_1_1_2_amount = safe_numeric(if("t3.8.1.1.2" %in% names(data)) t3.8.1.1.2 else 0),
      
      # 判断是否有商业补充医疗保险报销
      has_commercial_supplement_reimbursement = (
        t3_5_6_amount > 0 | 
          t3_6_1_1_2_amount > 0 | 
          t3_7_1_1_2_amount > 0 | 
          t3_8_1_1_2_amount > 0
      ),
      
      # 修正t3.1.0.5
      t3.1.0.5_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_supplement_reimbursement & 
          (is.na(t3.1.0.5) | t3.1.0.5 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.5)
      ),
      
      # ========================================================================
      # 修正t3.1.0.6（商业保险参与情况）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_7_amount = safe_numeric(if("t3.5.7" %in% names(data)) t3.5.7 else 0),
      t3_6_1_1_3_amount = safe_numeric(if("t3.6.1.1.3" %in% names(data)) t3.6.1.1.3 else 0),
      t3_7_1_1_3_amount = safe_numeric(if("t3.7.1.1.3" %in% names(data)) t3.7.1.1.3 else 0),
      t3_8_1_1_3_amount = safe_numeric(if("t3.8.1.1.3" %in% names(data)) t3.8.1.1.3 else 0),
      
      # 判断是否有商业保险报销
      has_commercial_insurance_reimbursement = (
        t3_5_7_amount > 0 | 
          t3_6_1_1_3_amount > 0 | 
          t3_7_1_1_3_amount > 0 | 
          t3_8_1_1_3_amount > 0
      ),
      
      # 修正t3.1.0.6
      t3.1.0.6_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_insurance_reimbursement & 
          (is.na(t3.1.0.6) | t3.1.0.6 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.6)
      ),
      
      # ========================================================================
      # 修正t3.1.0.7（其他途径）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_8_amount = safe_numeric(if("t3.5.8" %in% names(data)) t3.5.8 else 0),
      t3_6_1_1_4_amount = safe_numeric(if("t3.6.1.1.4" %in% names(data)) t3.6.1.1.4 else 0),
      t3_7_1_1_4_amount = safe_numeric(if("t3.7.1.1.4" %in% names(data)) t3.7.1.1.4 else 0),
      t3_8_1_1_4_amount = safe_numeric(if("t3.8.1.1.4" %in% names(data)) t3.8.1.1.4 else 0),
      
      # 判断是否有商业补充医疗保险报销
      has_commercial_supplement_reimbursement = (
        t3_5_8_amount > 0 | 
          t3_6_1_1_4_amount > 0 | 
          t3_7_1_1_4_amount > 0 | 
          t3_8_1_1_4_amount > 0
      ),
      
      # 修正t3.1.0.7
      t3.1.0.7_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_supplement_reimbursement & 
          (is.na(t3.1.0.7) | t3.1.0.7 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.7)
      )

    )

  # 更新原变量
  data$t3.1.0.4 <- data$t3.1.0.4_corrected
  data$t3.1.0.5 <- data$t3.1.0.5_corrected
  data$t3.1.0.6 <- data$t3.1.0.6_corrected
  data$t3.1.0.7 <- data$t3.1.0.7_corrected
  
  print("\n✅ 商业保险参与情况修正完成！")
  
  return(data)
}

correct_commercial_insurancedat1.1.1 <- function(data) {
  
  print("=== 修正商业保险参与情况 ===")
  
  # 安全数值转换函数
  safe_numeric <- function(x) {
    result <- suppressWarnings(as.numeric(as.character(x)))
    result[is.na(result)] <- 0
    return(result)
  }
  
  data <- data %>%
    mutate(
      # ========================================================================
      # 修正t3.1.0.4（公费医疗）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_5_amount = safe_numeric(if("t3.5.5" %in% names(data)) t3.5.5 else 0),
      t3_6_1_1_1_amount = safe_numeric(if("t3.6.1.1.1" %in% names(data)) t3.6.1.1.1 else 0),
      t3_7_1_1_1_amount = safe_numeric(if("t3.7.1.1.1" %in% names(data)) t3.7.1.1.1 else 0),
      
      
      # 判断是否有商业补充医疗保险报销
      has_commercial_supplement_reimbursement = (
        t3_5_5_amount > 0 | 
          t3_6_1_1_1_amount > 0 | 
          t3_7_1_1_1_amount > 0   
      ),
      
      # 修正t3.1.0.4
      t3.1.0.4_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_supplement_reimbursement & 
          (is.na(t3.1.0.4) | t3.1.0.4 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.4)
      ),
      
      
      # ========================================================================
      # 修正t3.1.0.5（商业补充医疗保险参与情况）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_6_amount = safe_numeric(if("t3.5.6" %in% names(data)) t3.5.6 else 0),
      t3_6_1_1_2_amount = safe_numeric(if("t3.6.1.1.2" %in% names(data)) t3.6.1.1.2 else 0),
      t3_7_1_1_2_amount = safe_numeric(if("t3.7.1.1.2" %in% names(data)) t3.7.1.1.2 else 0),
 
      
      # 判断是否有商业补充医疗保险报销
      has_commercial_supplement_reimbursement = (
        t3_5_6_amount > 0 | 
          t3_6_1_1_2_amount > 0 | 
          t3_7_1_1_2_amount > 0   
     
      ),
      
      # 修正t3.1.0.5
      t3.1.0.5_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_supplement_reimbursement & 
          (is.na(t3.1.0.5) | t3.1.0.5 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.5)
      ),
      
      # ========================================================================
      # 修正t3.1.0.6（商业保险参与情况）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_7_amount = safe_numeric(if("t3.5.7" %in% names(data)) t3.5.7 else 0),
      t3_6_1_1_3_amount = safe_numeric(if("t3.6.1.1.3" %in% names(data)) t3.6.1.1.3 else 0),
      t3_7_1_1_3_amount = safe_numeric(if("t3.7.1.1.3" %in% names(data)) t3.7.1.1.3 else 0),
     
      
      # 判断是否有商业保险报销
      has_commercial_insurance_reimbursement = (
        t3_5_7_amount > 0 | 
          t3_6_1_1_3_amount > 0 | 
          t3_7_1_1_3_amount > 0 
 
      ),
      
      # 修正t3.1.0.6
      t3.1.0.6_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_insurance_reimbursement & 
          (is.na(t3.1.0.6) | t3.1.0.6 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.6)
      ),
      
      # ========================================================================
      # 修正t3.1.0.7（其他途径）
      # ========================================================================
      
      # 检查相关报销金额是否大于0
      t3_5_8_amount = safe_numeric(if("t3.5.8" %in% names(data)) t3.5.8 else 0),
      t3_6_1_1_4_amount = safe_numeric(if("t3.6.1.1.4" %in% names(data)) t3.6.1.1.4 else 0),
      t3_7_1_1_4_amount = safe_numeric(if("t3.7.1.1.4" %in% names(data)) t3.7.1.1.4 else 0),
 
      
      # 判断是否有商业补充医疗保险报销
      has_commercial_supplement_reimbursement = (
        t3_5_8_amount > 0 | 
          t3_6_1_1_4_amount > 0 | 
          t3_7_1_1_4_amount > 0  
      
      ),
      
      # 修正t3.1.0.7
      t3.1.0.7_corrected = case_when(
        # 如果有报销金额且原值为0或空，则设为1
        has_commercial_supplement_reimbursement & 
          (is.na(t3.1.0.7) | t3.1.0.7 == 0) ~ 1,
        # 否则保持原值
        TRUE ~ safe_numeric(t3.1.0.7)
      )
    )
  
  
  # 更新原变量
  data$t3.1.0.4 <- data$t3.1.0.4_corrected
  data$t3.1.0.5 <- data$t3.1.0.5_corrected
  data$t3.1.0.6 <- data$t3.1.0.6_corrected
  data$t3.1.0.7 <- data$t3.1.0.7_corrected
  
  print("\n✅ 商业保险参与情况修正完成！")
  
  return(data)
}


dat2.1.1 <- correct_commercial_insurance(dat2.1.1)   
dat1.1.1 <- correct_commercial_insurancedat1.1.1(dat1.1.1)  


# ============================================================================
# 收入数据一致性异常值检验
# ============================================================================

#  累计收入计算
calculate_actual_time_matched <- function(data) {
  
  print("使用实际治疗时长进行时间匹配计算...")
   
  if(!"t2.3" %in% names(data)) {
    stop("缺少确诊日期变量 t2.3，无法计算实际治疗时长")
  }
  
  if(!"t5.2" %in% names(data)) {
    stop("缺少治疗时长截点变量 t5.2")
  }
  
  # 确定收入变量
  income_var <- NULL
  if("t4.3.2" %in% names(data)) {
    income_var <- "t4.3.2"
    print("使用确诊后收入 t4.3.2")
  } else if("t4.2.2" %in% names(data)) {
    income_var <- "t4.2.2"
    print("使用确诊前收入 t4.2.2（备选）")
  } else {
    stop("缺少收入变量")
  }
  
  # 改进的日期处理函数
  safe_date_conversion <- function(date_string) {
    if(is.na(date_string) || is.null(date_string) || date_string == "" || date_string == " ") {
      return(NA)
    }
    
    # 转换为字符串并清理
    date_str <- trimws(as.character(date_string))
    
    # 如果是空字符串，返回NA
    if(date_str == "" || date_str == " ") {
      return(NA)
    }
    
    # 尝试多种日期格式
    date_formats <- c(
      "%Y-%m-%d",     # 2023-01-01
      "%Y/%m/%d",     # 2023/01/01
      "%Y年%m月%d日",  # 2023年01月01日
      "%Y.%m.%d",     # 2023.01.01
      "%Y%m%d",       # 20230101
      "%m/%d/%Y",     # 01/01/2023
      "%d/%m/%Y"      # 01/01/2023
    )
    
    # 逐个尝试格式
    for(fmt in date_formats) {
      tryCatch({
        result <- as.Date(date_str, format = fmt)
        if(!is.na(result)) {
          return(result)
        }
      }, error = function(e) {
        # 继续尝试下一个格式
      })
    }
    
    # 如果所有格式都失败，尝试自动解析
    tryCatch({
      result <- as.Date(date_str)
      if(!is.na(result)) {
        return(result)
      }
    }, error = function(e) {
      # 最后的尝试也失败了
    })
    
    # 所有尝试都失败，返回NA
    return(NA)
  }
  
  # 处理确诊日期区间的函数
  process_diagnosis_date <- function(date_interval) {
    if(is.na(date_interval) || date_interval == "") {
      return(NA)
    }
    
    date_str <- trimws(as.character(date_interval))
    
    # 如果是区间格式
    if(grepl("至|到|~|-", date_str)) {
      # 分割日期区间
      separators <- c("至", "到", "~", "-")
      dates <- NULL
      
      for(sep in separators) {
        if(grepl(sep, date_str)) {
          dates <- unlist(strsplit(date_str, sep))
          break
        }
      }
      
      if(!is.null(dates) && length(dates) >= 2) {
        start_date <- safe_date_conversion(trimws(dates[1]))
        end_date <- safe_date_conversion(trimws(dates[2]))
        
        if(!is.na(start_date) && !is.na(end_date)) {
          # 返回中间日期
          middle_date <- start_date + as.numeric(end_date - start_date) / 2
          return(middle_date)
        }
      }
    }
    
    # 如果不是区间，直接转换
    return(safe_date_conversion(date_str))
  }
  
  data %>%
    mutate(
    
      # 确诊日期处理
      diagnosis_date = sapply(as.character(t2.3), process_diagnosis_date),
      diagnosis_date = as.Date(diagnosis_date, origin = "1970-01-01"),
      
      # 治疗时长截点日期处理
      cutoff_date = sapply(as.character(t5.2), safe_date_conversion),
      cutoff_date = as.Date(cutoff_date, origin = "1970-01-01"),
      
      # 实际治疗时长（从确诊日期到截点日期）
      actual_treatment_days = case_when(
        !is.na(diagnosis_date) & !is.na(cutoff_date) ~ 
          as.numeric(difftime(cutoff_date, diagnosis_date, units = "days")),
        TRUE ~ NA_real_
      ),
      
      # 确保治疗时长为正数
      actual_treatment_months = case_when(
        !is.na(actual_treatment_days) & actual_treatment_days > 0 ~ 
          actual_treatment_days / 30.44,
        TRUE ~ NA_real_
      ),
      
      # 治疗时长分组
      treatment_duration_category = case_when(
        is.na(actual_treatment_months) ~ "未知",
        actual_treatment_months <= 3 ~ "短期(≤3个月)",
        actual_treatment_months <= 6 ~ "中短期(3-6个月)",
        actual_treatment_months <= 12 ~ "中期(6-12个月)",
        actual_treatment_months <= 24 ~ "长期(1-2年)",
        actual_treatment_months > 24 ~ "超长期(>2年)",
        TRUE ~ "其他"
      ),
      
      # 确诊后月收入（元）
      monthly_income = ifelse(is.na(.data[[income_var]]) | .data[[income_var]] < 0, 
                              NA, .data[[income_var]] ),
      
      # 分母：治疗期间家庭总收入（元）
      total_income_actual_period = case_when(
        !is.na(monthly_income) & !is.na(actual_treatment_months) & actual_treatment_months > 0 ~ 
          monthly_income * actual_treatment_months,
        TRUE ~ NA_real_
      ) 
      
      # # 实际时间匹配的经济负担强度
      # burden_intensity_actual = case_when(
      #   is.na(total_income_actual_period) | total_income_actual_period <= 0 ~ NA_real_,
      #   TRUE ~ medical_cost_total / total_income_actual_period * 100
      # ),
      # 
      # # 灾难性支出判断（40%阈值）
      # catastrophic_actual = case_when(
      #   is.na(burden_intensity_actual) ~ FALSE,
      #   burden_intensity_actual > 40 ~ TRUE,
      #   TRUE ~ FALSE
      # ),
      # 
      # # 不同阈值的灾难性支出
      # catastrophic_40_actual = burden_intensity_actual > 40,
      # 
      # # 负担程度分级
      # burden_category_actual = case_when(
      #   is.na(burden_intensity_actual) ~ "无法计算",
      #   burden_intensity_actual <= 10 ~ "轻度负担(≤10%)",
      #   burden_intensity_actual <= 25 ~ "中度负担(10-25%)", 
      #   burden_intensity_actual <= 40 ~ "重度负担(25-40%)",
      #   burden_intensity_actual > 40 ~ "灾难性负担(>40%)",
      #   TRUE ~ "其他"
      # ),
      # 
      # # 月均医疗支出
      # monthly_medical_cost = case_when(
      #   !is.na(actual_treatment_months) & actual_treatment_months > 0 ~ 
      #     medical_cost_total / actual_treatment_months,
      #   TRUE ~ NA_real_
      # ),
      # 
      # # 月均医疗支出占月收入比例
      # monthly_burden_ratio_actual = case_when(
      #   !is.na(monthly_medical_cost) & !is.na(monthly_income) & monthly_income > 0 ~ 
      #     monthly_medical_cost / monthly_income * 100,
      #   TRUE ~ NA_real_
      # )
    )
}

dat2.1.1 <- calculate_actual_time_matched(dat2.1.1)   
dat1.1.1 <- calculate_actual_time_matched(dat1.1.1) 


## 出现症状到确诊时间
calculate_actual_time_symptom <- function( data ){

  # 确定收入变量
  income_var <- NULL
  if("t4.3.2" %in% names(data)) {
    income_var <- "t4.3.2"
    print("使用出现症状后收入 t4.3.2")
  } else if("t4.2.2" %in% names(data)) {
    income_var <- "t4.2.2"
    print("使用出现症状前收入 t4.2.2（备选）")
  } else {
    stop("缺少收入变量")
  }
 
  # 改进的日期处理函数
  safe_date_conversion <- function(date_string) {
    if(is.na(date_string) || is.null(date_string) || date_string == "" || date_string == " ") {
      return(NA)
    }
    
    # 转换为字符串并清理
    date_str <- trimws(as.character(date_string))
    
    # 如果是空字符串，返回NA
    if(date_str == "" || date_str == " ") {
      return(NA)
    }
    
    # 尝试多种日期格式
    date_formats <- c(
      "%Y-%m-%d",     # 2023-01-01
      "%Y/%m/%d",     # 2023/01/01
      "%Y年%m月%d日",  # 2023年01月01日
      "%Y.%m.%d",     # 2023.01.01
      "%Y%m%d",       # 20230101
      "%m/%d/%Y",     # 01/01/2023
      "%d/%m/%Y"      # 01/01/2023
    )
    
    # 逐个尝试格式
    for(fmt in date_formats) {
      tryCatch({
        result <- as.Date(date_str, format = fmt)
        if(!is.na(result)) {
          return(result)
        }
      }, error = function(e) {
        # 继续尝试下一个格式
      })
    }
    
    # 如果所有格式都失败，尝试自动解析
    tryCatch({
      result <- as.Date(date_str)
      if(!is.na(result)) {
        return(result)
      }
    }, error = function(e) {
      # 最后的尝试也失败了
    })
    
    # 所有尝试都失败，返回NA
    return(NA)
  }
  
  # 处理确诊日期区间的函数
  process_diagnosis_date <- function(date_interval) {
    if(is.na(date_interval) || date_interval == "") {
      return(NA)
    }
    
    date_str <- trimws(as.character(date_interval))
    
    # 如果是区间格式
    if(grepl("至|到|/", date_str)) {
      # 分割日期区间
      separators <- c("至", "到" , "/")
      dates <- NULL
      
      for(sep in separators) {
        if(grepl(sep, date_str)) {
          dates <- unlist(strsplit(date_str, sep))
          break
        }
      }
      
      if(!is.null(dates) && length(dates) >= 2) {
        start_date <- safe_date_conversion(trimws(dates[1]))
        end_date <- safe_date_conversion(trimws(dates[2]))
        
        if(!is.na(start_date) && !is.na(end_date)) {
          # 返回中间日期
          middle_date <- start_date + as.numeric(end_date - start_date) / 2
          return(middle_date)
        }
      }
    }
    
    # 如果不是区间，直接转换
    return(safe_date_conversion(date_str))
  }

  data %>%
    mutate(
      
      # symptom日期处理
      symptom_date = sapply(as.character(t2.4.1), process_diagnosis_date),
      symptom_date = as.Date(symptom_date, origin = "1970-01-01"),
      
      # examination日期处理
      examination_date = sapply(as.character(t2.4.0), process_diagnosis_date),
      examination_date = as.Date(examination_date, origin = "1970-01-01"),
      
      # 治疗时长截点日期处理
      cutoff_date = sapply(as.character(t5.2), safe_date_conversion),
      cutoff_date = as.Date(cutoff_date, origin = "1970-01-01"),
      
      # 实际治疗时长（从确诊日期到截点日期）
      actual_symptom_days = case_when(
        !is.na(symptom_date) & !is.na(cutoff_date) ~ 
          as.numeric(difftime(cutoff_date, symptom_date, units = "days")),
  
        !is.na(examination_date) & !is.na(cutoff_date) ~ 
          as.numeric(difftime(cutoff_date, examination_date, units = "days")),
          
        TRUE ~ NA_real_
      ),
      
      # 确保治疗时长为正数
      actual_symptom_months = case_when(
        !is.na(actual_symptom_days) & actual_symptom_days > 0 ~ 
          actual_symptom_days / 30.44,
        TRUE ~ NA_real_
      ),
      
      # 治疗时长分组
      treatment_duration_category = case_when(
        is.na(actual_symptom_months) ~ "未知",
        actual_symptom_months <= 3 ~ "短期(≤3个月)",
        actual_symptom_months <= 6 ~ "中短期(3-6个月)",
        actual_symptom_months <= 12 ~ "中期(6-12个月)",
        actual_symptom_months <= 24 ~ "长期(1-2年)",
        actual_symptom_months > 24 ~ "超长期(>2年)",
        TRUE ~ "其他"
      ),
 
      # 确诊后月收入（元）
      monthly_income = ifelse(is.na(.data[[income_var]]) | .data[[income_var]] < 0, 
                              NA, .data[[income_var]]),
      # 分母：治疗期间家庭总收入（元）
      total_income_symptom_period = case_when(
        !is.na(monthly_income) & !is.na(actual_symptom_months) & actual_symptom_months > 0 ~ 
          monthly_income * actual_symptom_months,
        TRUE ~ NA_real_
      ) 
    )
}

dat1.1.1 <- calculate_actual_time_symptom (dat1.1.1)
dat2.1.1 <- calculate_actual_time_symptom (dat2.1.1)

check_income_consistency <- function(data) {
  
  print("=== 收入数据一致性异常值检验 ===")
  
  # 检查必要变量是否存在
  required_vars <- c("t4.5.1.1", "t4.6.1.1", "t4.2.2", "t4.3.2", "t5.8")
  missing_vars <- required_vars[!required_vars %in% names(data)]
  
  if(length(missing_vars) > 0) {
    print(paste("❌ 缺少必要变量:", paste(missing_vars, collapse = ", ")))
    return(NULL)
  }
  
  # 安全数值转换函数
  safe_numeric <- function(x) {
    result <- suppressWarnings(as.numeric(as.character(x)))
    result[is.na(result)] <- 0
    return(result)
  }
  
  # 计算一致性检验
  result_data <- data %>%
    mutate(
      # 转换为数值型
      t4_5_1_1 = safe_numeric(t4.5.1.1),
      t4_6_1_1 = safe_numeric(t4.6.1.1),
      t4_5_1_2 = safe_numeric(t4.5.1.2),
      t4_6_1_2 = safe_numeric(t4.6.1.2),
      t4_2_2 = safe_numeric(t4.2.2),
      t4_3_2 = safe_numeric(t4.3.2),
      
      # 计算加和
      income_sum_prior = t4_5_1_1 + t4_6_1_1,  # 确诊前父母收入加和
      income_sum_post = t4_5_1_2 + t4_6_1_2,   # 确诊后父母收入加和
      
      # ========================================================================
      # 与t4.2.2的一致性检验（确诊前收入）
      # ========================================================================
      
      # 绝对差异
      diff_with_t4_2_2 = abs(income_sum_prior - t4_2_2),
      
      # 相对差异（%）
      relative_diff_t4_2_2 = case_when(
        is.na(t4_2_2) | t4_2_2 == 0 ~ NA_real_,
        TRUE ~ diff_with_t4_2_2 / t4_2_2 * 100
      ),
      
      # 一致性判断（确诊前收入）
      consistency_t4_2_2 = case_when(
        is.na(income_sum_prior) | is.na(t4_2_2) ~ "无法比较",
        income_sum_prior == 0 & t4_2_2 == 0 ~ "一致（均为0）",
        diff_with_t4_2_2 <= 0.01 ~ "完全一致",
        relative_diff_t4_2_2 <= 5 ~ "基本一致(≤5%)",
        relative_diff_t4_2_2 <= 20 ~ "存在差异(5-20%)",
        relative_diff_t4_2_2 <= 50 ~ "明显差异(20-50%)",
        relative_diff_t4_2_2 > 50 ~ "严重不一致(>50%)",
        TRUE ~ "其他"
      ),
      
      # ========================================================================
      # 与t4.3.2的一致性检验（确诊后收入）
      # ========================================================================
      
      # 绝对差异
      diff_with_t4_3_2 = abs(income_sum_post - t4_3_2),
      
      # 相对差异（%）
      relative_diff_t4_3_2 = case_when(
        is.na(t4_3_2) | t4_3_2 == 0 ~ NA_real_,
        TRUE ~ diff_with_t4_3_2 / t4_3_2 * 100
      ),
      
      # 一致性判断（确诊后收入）
      consistency_t4_3_2 = case_when(
        is.na(income_sum_post) | is.na(t4_3_2) ~ "无法比较",
        income_sum_post == 0 & t4_3_2 == 0 ~ "一致（均为0）",
        diff_with_t4_3_2 <= 0.01 ~ "完全一致",
        relative_diff_t4_3_2 <= 5 ~ "基本一致(≤5%)",
        relative_diff_t4_3_2 <= 20 ~ "存在差异(5-20%)",
        relative_diff_t4_3_2 <= 50 ~ "明显差异(20-50%)",
        relative_diff_t4_3_2 > 50 ~ "严重不一致(>50%)",
        TRUE ~ "其他"
      ),
      
      # ========================================================================
      # 异常标记
      # ========================================================================
      
      # 是否为异常记录
      is_abnormal = (consistency_t4_2_2 %in% c("明显差异(20-50%)", "严重不一致(>50%)")) |
        (consistency_t4_3_2 %in% c("明显差异(20-50%)", "严重不一致(>50%)")),
      
      # 异常类型
      abnormal_type = case_when(
        !is_abnormal ~ "正常",
        consistency_t4_2_2 %in% c("明显差异(20-50%)", "严重不一致(>50%)") &
          consistency_t4_3_2 %in% c("明显差异(20-50%)", "严重不一致(>50%)") ~ "两项收入均异常",
        consistency_t4_2_2 %in% c("明显差异(20-50%)", "严重不一致(>50%)") ~ "确诊前收入异常",
        consistency_t4_3_2 %in% c("明显差异(20-50%)", "严重不一致(>50%)") ~ "确诊后收入异常",
        TRUE ~ "其他异常"
      )
    )
  
  # 统计一致性分布
  print("\n=== 与t4.2.2（确诊前收入）一致性分布 ===")
  consistency_t4_2_2_table <- table(result_data$consistency_t4_2_2)
  for(i in 1:length(consistency_t4_2_2_table)) {
    cat("  ", names(consistency_t4_2_2_table)[i], ":", consistency_t4_2_2_table[i], "例\n")
  }
  
  print("\n=== 与t4.3.2（确诊后收入）一致性分布 ===")
  consistency_t4_3_2_table <- table(result_data$consistency_t4_3_2)
  for(i in 1:length(consistency_t4_3_2_table)) {
    cat("  ", names(consistency_t4_3_2_table)[i], ":", consistency_t4_3_2_table[i], "例\n")
  }

 return(result_data)
}

# 执行收入一致性检验
if(exists("dat2.1.1")) {
  
  print("开始收入数据一致性异常值检验...")
  
  # 执行一致性检验
  dat2.1.1_with_consistency <- check_income_consistency(dat2.1.1)
  
  if(!is.null(dat2.1.1_with_consistency)) {
    # 更新数据
    dat2.1.1 <- dat2.1.1_with_consistency
    print("\n✅ 收入一致性检验完成！结果已添加到数据中")
  }
  
} else {
  print("❌ dat2.1.1数据不存在")
}

if(exists("dat1.1.1")) {
  
  print("开始收入数据一致性异常值检验...")
  
  # 执行一致性检验
  dat1.1.1_with_consistency <- check_income_consistency(dat1.1.1)
  
  if(!is.null(dat1.1.1_with_consistency)) {
    # 更新数据
    dat1.1.1 <- dat1.1.1_with_consistency
    print("\n✅ 收入一致性检验完成！结果已添加到数据中")
  }
  
} else {
  print("❌ dat1.1.1数据不存在")
}
# 显示结果

print("\n提取异常记录 ===")
# # 提取异常记录
abnormal_records <- dat2.1.1 %>%
  filter(is_abnormal == TRUE) %>%
  mutate(
    # 先计算判断理由
    判断理由 = case_when(
      abnormal_type == "两项收入均异常" ~ paste0("确诊前：父母收入加和(", income_sum_prior, ")与确诊前收入(", t4_2_2, ")差异",
                                          round(relative_diff_t4_2_2, 1), "%；确诊后：父母收入加和(", income_sum_post,
                                          ")与确诊后收入(", t4_3_2, ")差异", round(relative_diff_t4_3_2, 1), "%，均超过20%"),
      abnormal_type == "确诊前收入异常" ~ paste0("父母收入加和(", income_sum_prior, ")与确诊前收入(", t4_2_2,
                                          ")差异", round(relative_diff_t4_2_2, 1), "%，超过20%"),
      abnormal_type == "确诊后收入异常" ~ paste0("父母收入加和(", income_sum_post, ")与确诊后收入(", t4_3_2,
                                          ")差异", round(relative_diff_t4_3_2, 1), "%，超过20%"),
      TRUE ~ "其他原因"
    )
  ) %>%
  select(
    `问卷号_t5_8` = t5.8,
    `t4_5_1_1_确诊前父亲收入` = t4_5_1_1,
    `t4_6_1_1_确诊前母亲收入` = t4_6_1_1,
    `t4_5_1_2_确诊后父亲收入` = t4_5_1_2,
    `t4_6_1_2_确诊后母亲收入` = t4_6_1_2,
    `确诊前父母收入加和` = income_sum_prior,
    `确诊后父母收入加和` = income_sum_post,
    `t4_2_2_确诊前家庭收入` = t4_2_2,
    `t4_3_2_确诊后家庭收入` = t4_3_2,
    `与确诊前差异_元` = diff_with_t4_2_2,
    `与确诊前相对差异_百分比` =  relative_diff_t4_2_2 ,
    `确诊前一致性判断` = consistency_t4_2_2,
    `与确诊后差异_元` = diff_with_t4_3_2,
    `与确诊后相对差异_百分比` =  relative_diff_t4_3_2 ,
    `确诊后一致性判断` = consistency_t4_3_2,
    `异常类型` = abnormal_type
    
  )
if(nrow(abnormal_records) > 0) {
  print(paste("\n⚠️ 发现", nrow(abnormal_records), "条收入一致性异常记录"))
  
  # 按异常类型统计
  print("\n=== 异常类型分布 ===")
  abnormal_type_table <- table(abnormal_records$异常类型)
  for(i in 1:length(abnormal_type_table)) {
    cat("  ", names(abnormal_type_table)[i], ":", abnormal_type_table[i], "例\n")
  }
  
  # 保存异常记录
  write.csv(abnormal_records, "收入一致性异常记录.csv", row.names = FALSE, fileEncoding = "UTF-8")
  print("\n异常记录详情已保存至: 收入一致性异常记录.csv")
  
  # 显示前10个异常记录
  print("\n=== 前10个异常记录 ===")
  print(head(abnormal_records %>%
               select(`问卷号_t5_8`, `确诊前父母收入加和`,  `确诊后父母收入加和` ,`t4_2_2_确诊前家庭收入`, `t4_3_2_确诊后家庭收入`,
                      `与确诊前相对差异_百分比`, `与确诊后相对差异_百分比`, `异常类型`), 10))
    
  # 显示最严重的异常（相对差异最大）
  print("\n=== 最严重的5个异常记录 ===")
  most_severe <- abnormal_records %>%
    mutate(max_relative_diff = pmax(`与确诊前相对差异_百分比`, `与确诊后相对差异_百分比`, na.rm = TRUE)) %>%
    arrange(desc(max_relative_diff)) %>%
    head(5) %>%
    select(问卷号_t5_8   )
  
  print(most_severe)
  
} else {
  print("\n✅ 未发现收入一致性异常记录")
}
  
## 根据报表手动修改 父母一方、家庭总收入的确诊前收入异常， 单位写错  筛选条件：小于100元
 dat2.1.1 = dat2.1.1 %>%
  mutate(
   t4.2.2 = case_when(
      t5.8 == "006095" ~ 35000,
      t5.8 == "005078" ~ 13000,
      t5.8 == "011234" ~ 8000,
      t5.8 == "009514" ~ 20000,
      t5.8 == "007959" ~ 5000,
      t5.8 == "005548" ~ 20000, #根据父母收入修改
      t5.8 == "004925" ~ 8000, #根据父母收入修改
      t5.8 == "004804" ~ 10000, #根据父母收入修改
      TRUE ~ t4.2.2
    ),
    #筛选条件：小于1000元
   t4.5.1.1 = case_when(
      t5.8 == "009104" ~ 10000,
      t5.8 == "005341" ~ 7000,
      t5.8 == "011289" ~ 3000,
      t5.8 == "005179" ~ 2000, 
      t5.8 == "005631" ~ 3000, 
      t5.8 == "005384" ~ 4000,  
      TRUE ~ t4.5.1.1   # 其它情况保持原值
    ),
    #筛选条件：小于1000元
    t4.6.1.1 = case_when(
      t5.8 == "008934" ~ 3500,
      t5.8 == "007425" ~ 10000,
      t5.8 == "006680" ~ 3000,
      t5.8 == "005797" ~ 10000,
      t5.8 == "005794" ~ 5000,
      t5.8 == "004823" ~ 10000,
      t5.8 == "004745" ~ 10000,
      t5.8 == "004602" ~ 3000,
      t5.8 == "005407" ~ 800,
      TRUE ~ t4.6.1.1   # 其它情况保持原值
    ),
   t4.3.2 = case_when(
      t5.8 == "009502" ~ 100000,
      t5.8 == "006095" ~ 35000,
      t5.8 == "005078" ~ 10000,
      t5.8 == "010040" ~ 10000,
      t5.8 == "011234" ~ 8000,
      t5.8 == "009888" ~ 5000,
      t5.8 == "005886" ~ 1000,
      t5.8 == "005762" ~ 5000,
      t5.8 == "005407" ~ 3000,
      t5.8 == "005059" ~ 8000,
      t5.8 == "004990" ~ 3000,
      t5.8 == "004628" ~ 6300,
      TRUE ~ t4.3.2
    ),
    ##筛选条件：小于1000元
    t4.5.1.2 = case_when(
      t5.8 == "007257" ~ 3000,
      t5.8 == "006665" ~ 5000,
      t5.8 == "005905" ~ 6660,
      t5.8 == "005563" ~ 1000,
      TRUE ~ t4.5.1.2   # 其它情况保持原值
    ),
    ##筛选条件：小于1000元
    t4.6.1.2 = case_when(
      t5.8 == "005563" ~ 1000 ,
      t5.8 == "005081" ~ 5000 ,
      TRUE ~ t4.6.1.2   # 其它情况保持原值
    )
  )

  

abnormal_records <- dat1.1.1 %>%
  filter(is_abnormal == TRUE) %>%
  mutate(
    # 先计算判断理由
    判断理由 = case_when(
      abnormal_type == "两项收入均异常" ~ paste0("确诊前：父母收入加和(", income_sum_prior, ")与确诊前收入(", t4_2_2, ")差异",
                                          round(relative_diff_t4_2_2, 1), "%；确诊后：父母收入加和(", income_sum_post,
                                          ")与确诊后收入(", t4_3_2, ")差异", round(relative_diff_t4_3_2, 1), "%，均超过20%"),
      abnormal_type == "确诊前收入异常" ~ paste0("父母收入加和(", income_sum_prior, ")与确诊前收入(", t4_2_2,
                                          ")差异", round(relative_diff_t4_2_2, 1), "%，超过20%"),
      abnormal_type == "确诊后收入异常" ~ paste0("父母收入加和(", income_sum_post, ")与确诊后收入(", t4_3_2,
                                          ")差异", round(relative_diff_t4_3_2, 1), "%，超过20%"),
      TRUE ~ "其他原因"
    )
  ) %>%
  select(
    `问卷号_t5_8` = t5.8,
    `t4_5_1_1_确诊前父亲收入` = t4_5_1_1,
    `t4_6_1_1_确诊前母亲收入` = t4_6_1_1,
    `t4_5_1_2_确诊后父亲收入` = t4_5_1_2,
    `t4_6_1_2_确诊后母亲收入` = t4_6_1_2,
    `确诊前父母收入加和` = income_sum_prior,
    `确诊后父母收入加和` = income_sum_post,
    `t4_2_2_确诊前家庭收入` = t4_2_2,
    `t4_3_2_确诊后家庭收入` = t4_3_2,
    `与确诊前差异_元` = diff_with_t4_2_2,
    `与确诊前相对差异_百分比` =  relative_diff_t4_2_2 ,
    `确诊前一致性判断` = consistency_t4_2_2,
    `与确诊后差异_元` = diff_with_t4_3_2,
    `与确诊后相对差异_百分比` =  relative_diff_t4_3_2 ,
    `确诊后一致性判断` = consistency_t4_3_2,
    `异常类型` = abnormal_type
    
  )
if(nrow(abnormal_records) > 0) {
  print(paste("\n⚠️ 发现", nrow(abnormal_records), "条收入一致性异常记录"))
  
  # 按异常类型统计
  print("\n=== 异常类型分布 ===")
  abnormal_type_table <- table(abnormal_records$异常类型)
  for(i in 1:length(abnormal_type_table)) {
    cat("  ", names(abnormal_type_table)[i], ":", abnormal_type_table[i], "例\n")
  }
  
  # 保存异常记录
  write.csv(abnormal_records, "dat1.1.1收入一致性异常记录.csv", row.names = FALSE, fileEncoding = "UTF-8")
  print("\n异常记录详情已保存至: dat1.1.1收入一致性异常记录.csv")
  
  # 显示前10个异常记录
  print("\n=== 前10个异常记录 ===")
  print(head(abnormal_records %>%
               select(`问卷号_t5_8`, `确诊前父母收入加和`,  `确诊后父母收入加和` ,`t4_2_2_确诊前家庭收入`, `t4_3_2_确诊后家庭收入`,
                      `与确诊前相对差异_百分比`, `与确诊后相对差异_百分比`, `异常类型`), 10))
  
  # 显示最严重的异常（相对差异最大）
  print("\n=== 最严重的5个异常记录 ===")
  most_severe <- abnormal_records %>%
    mutate(max_relative_diff = pmax(`与确诊前相对差异_百分比`, `与确诊后相对差异_百分比`, na.rm = TRUE)) %>%
    arrange(desc(max_relative_diff)) %>%
    head(5) %>%
    select(问卷号_t5_8 )
  
  print(most_severe)
  
} else {
  print("\n✅ 未发现收入一致性异常记录")
}

dat1.1.1 = dat1.1.1 %>%
  mutate(
  t4.5.1.1 = case_when(
      t5.8 == "011285" ~ t4.5.1.1*10, 
      t5.8 == "008759" ~ t4.5.1.1*10, 
      t5.8 == "007957" ~ t4.5.1.1*10, 
      t5.8 == "005815" ~ t4.5.1.1*10, 
      TRUE ~ t4.5.1.1
  ), 
   t4.6.1.1 = case_when(
      t5.8 == "009160" ~ t4.6.1.1*100, 
      t5.8 == "006172" ~ t4.6.1.1*1000, 
 
      TRUE ~ t4.6.1.1
  ), 
  t4.2.2 = case_when(
      t5.8 == "007396" ~ t4.2.2*100, 
      t5.8 == "006172" ~ 7000, #根据父母收入修改
      TRUE ~ t4.2.2
  ), 
  t4.5.1.2 = case_when(
    t5.8 == "009821" ~ t4.5.1.2*10, 
    t5.8 == "009326" ~ t4.5.1.2*10, 
    t5.8 == "007957" ~ t4.5.1.2*10, 
    TRUE ~ t4.5.1.2
  ), 
   t4.6.1.2 = case_when(
    t5.8 == "006172" ~ t4.6.1.2*10000, 
    TRUE ~ t4.6.1.2
  ) 
  )
 
# 定义函数：合并单个变量并删除原始哑变量列
  merge_one <- function(df, base, anchor, keep_extra = NULL) {
    y <- paste0(base, ".y")
    
    # 定位 anchor
    cols <- colnames(df)
    anchor_pos <- which(cols == anchor)
    
    # 选择列顺序
    if (!is.null(keep_extra)) {
      df <- df %>%
        select(1:(anchor_pos-1), all_of(y), all_of(keep_extra), (anchor_pos+length(keep_extra)+1):ncol(.))
    } else {
      df <- df %>%
        select(1:(anchor_pos-1), all_of(y), (anchor_pos+1):ncol(.))
    }
    
    # 重命名 y 列为 base
    df <- df %>% rename(!!base := all_of(y))
    
    # 删除所有原始哑变量列
    df <- df %>% select(-starts_with(paste0(base, "-")))
    
    return(df)
  }

  # 准备合并的变量信息
  merge_list <- list(
    list(base="t1.1",   anchor="t1.1-男"),
    list(base="t1.4",   anchor="t1.4-城镇户口"),
    list(base="t1.5",   anchor="t1.5-住院"),
    list(base="t1.6",   anchor="t1.6-现场填写"),
    list(base="t1.7",   anchor="t1.7-线上填写"),
    list(base="t2.3.1", anchor="t2.3.1-从病历或其他记录中获得"),
    list(base="t2.4",   anchor="t2.4-出现各类异常症状后前往医院，如咳嗽、发热、疼痛、紫癜、红肿、肿块等"),
    list(base="t2.4.0.1", anchor="t2.4.0.1-从病历或其他记录中获得"),
    list(base="t2.4.1.1", anchor="t2.4.1.1-从病历或其他记录中获得"),
    list(base="t2.4.2.1", anchor="t2.4.2.1-是"),
    list(base="t2.4.3.1", anchor="t2.4.3.1-从病历或其他记录中获得"),
    list(base="t2.5.1.1.1", anchor="t2.5.1.1.1-从病历或其他记录中获得"),
    list(base="t2.5.2.1.1", anchor="t2.5.2.1.1-从病历或其他记录中获得"),
    list(base="t2.5.3.1.1", anchor="t2.5.3.1.1-从病历或其他记录中获得"),
    list(base="t2.5.4.1.1", anchor="t2.5.4.1.1-从病历或其他记录中获得"),
    list(base="t2.5.5.1.1", anchor="t2.5.5.1.1-从病历或其他记录中获得"),
    list(base="t2.5.6.1.1", anchor="t2.5.6.1.1-从病历或其他记录中获得"),
    list(base="t2.5.7.1.1", anchor="t2.5.7.1.1-从病历或其他记录中获得"),
    list(base="t2.6", anchor="t2.6-有", keep_extra=c("t2.6.1.1","t2.6.1.2")),
    list(base="t3.1.2.1", anchor="t3.1.2.1-是", keep_extra="t3.1.2.1_1"),
    list(base="t3.2", anchor="t3.2-是", keep_extra="t3.2_1"),
    list(base="t3.3", anchor="t3.3-是", keep_extra="t3.3_1"),
    list(base="t3.6", anchor="t3.6-是"),
    list(base="t3.6.1", anchor="t3.6.1-是"),
    list(base="t3.7", anchor="t3.7-是"),
    list(base="t3.7.1", anchor="t3.7.1-是"),
    list(base="t3.8", anchor="t3.8-是"),
    list(base="t3.8.1", anchor="t3.8.1-是"),
    list(base="t4.5", anchor="t4.5-毫无影响"),
    list(base="t4.6", anchor="t4.6-毫无影响"),
    list(base="t4.8", anchor="t4.8-未上过学"),
    list(base="t4.9", anchor="t4.9-国家机关和企事业单位负责人"),
    list(base="t4.11", anchor="t4.11-未上过学"),
    list(base="t4.12", anchor="t4.12-国家机关和企事业单位负责人") ,
    list(base="t4.13", anchor="t4.13-有", keep_extra="t4.13_1"  ),
    list(base="d6", anchor="d6-是", keep_extra="d6_1" ),
    list(base="d7", anchor="d7-是", keep_extra="d7_1" )
  )
  
  # 循环合并
 
  for (m in merge_list) {
    dat2.1.1  <- merge_one(
      df = dat2.1.1,
      base = m$base,
      anchor = m$anchor,
      keep_extra = m$keep_extra 
 
    )
  }
  # 删除 t4.13.1- 请假等额外列
  dat2.1.1  <- dat2.1.1 %>% select(-c(`t4.13.1-请假`:`t4.13.1_4`)) 
  
  
  # 准备合并的变量信息
  merge_list <- list(
    list(base="t1.1",   anchor="t1.1-男"),
    list(base="t1.4",   anchor="t1.4-城镇户口"),
    list(base="t1.5",   anchor="t1.5-住院"),
    list(base="t1.6",   anchor="t1.6-现场填写"),
    list(base="t1.7",   anchor="t1.7-线上填写"),
    list(base="t2.3.1", anchor="t2.3.1-从病历或其他记录中获得"),
    list(base="t2.4",   anchor="t2.4-出现各类异常症状后前往医院，如咳嗽、发热、疼痛、紫癜、红肿、肿块等"),
    list(base="t2.4.0.1", anchor="t2.4.0.1-从病历或其他记录中获得"),
    list(base="t2.4.1.1", anchor="t2.4.1.1-从病历或其他记录中获得"),
    list(base="t2.4.2.1", anchor="t2.4.2.1-是"),

    list(base="t3.1.2.1", anchor="t3.1.2.1-是", keep_extra="t3.1.2.1_1"),
    list(base="t3.2", anchor="t3.2-是", keep_extra="t3.2_1"),
    list(base="t3.3", anchor="t3.3-是", keep_extra="t3.3_1"),
    list(base="t3.6", anchor="t3.6-是"),
    list(base="t3.6.1", anchor="t3.6.1-是"),
    list(base="t3.7", anchor="t3.7-是"),
    list(base="t3.7.1", anchor="t3.7.1-是"),

    list(base="t4.5", anchor="t4.5-毫无影响"),
    list(base="t4.6", anchor="t4.6-毫无影响"),
    list(base="t4.8", anchor="t4.8-未上过学"),
    list(base="t4.9", anchor="t4.9-国家机关和企事业单位负责人"),
    list(base="t4.11", anchor="t4.11-未上过学"),
    list(base="t4.12", anchor="t4.12-国家机关和企事业单位负责人") ,
    list(base="t4.13", anchor="t4.13-有", keep_extra="t4.13_1" ),
    list(base="d6", anchor="d6-是", keep_extra="d6_1" ),
    list(base="d7", anchor="d7-是", keep_extra="d7_1" )
  )
  
  for (m in merge_list) {
    dat1.1.1  <- merge_one(
      df = dat1.1.1,
      base = m$base,
      anchor = m$anchor,
      keep_extra = m$keep_extra 
      
    )
  }
  
##删除 中间变量
dat2.1.1 <-  dat2.1.1 %>% 
  select(- c( diff_with_t4_2_2:abnormal_type , t4_5_1_1:t4_3_2,  t3_4_value_safe:t3.1.0.6_corrected, t3_4_value:difference_with_t34_personal) )
  
dat1.1.1 <-  dat1.1.1 %>% 
  select(- c( diff_with_t4_2_2:abnormal_type , t4_5_1_1:t4_3_2,  t3_4_value_safe:t3.1.0.6_corrected, t3_4_value:difference_with_t34_personal) )
 

### 拆分 地址
dat2.1.1 <-dat2.1.1 %>%
  separate(t1.4.3, into = c("t1.4.3_province", "t1.4.3_city", "t1.4.3_county"), sep = "#", remove = FALSE)%>%
  relocate(t1.4.3_province:t1.4.3_county, .after = t1.4.3)%>%
  separate(t1.3, into = c("t1.3_province", "t1.3_city", "t1.3_county"), sep = "#", remove = FALSE) %>%
  relocate(t1.3_province:t1.3_county, .after = t1.3)%>%
  separate(t3.1.0.1_1, into = c("t3.1.0.1_1_province", "t3.1.0.1_1_city", "t3.1.0.1_1_county"), sep = "#", remove = FALSE)%>%
  relocate(t3.1.0.1_1_province:t3.1.0.1_1_county, .after = t3.1.0.1_1)%>%
separate(t3.1.0.5_1, into = c("t3.1.0.5_1_province", "t3.1.0.5_1_city", "t3.1.0.5_1_county"), sep = "#", remove = FALSE)%>%
  relocate(t3.1.0.5_1_province:t3.1.0.5_1_county, .after = t3.1.0.5_1)


dat1.1.1 <-dat1.1.1 %>%
  separate(t1.4.3, into = c("t1.4.3_province", "t1.4.3_city", "t1.4.3_county"), sep = "#", remove = FALSE)%>%
  relocate(t1.4.3_province:t1.4.3_county, .after = t1.4.3)%>%
  separate(t1.3, into = c("t1.3_province", "t1.3_city", "t1.3_county"), sep = "#", remove = FALSE) %>%
  relocate(t1.3_province:t1.3_county, .after = t1.3)%>%
  separate(t3.1.0.1_1, into = c("t3.1.0.1_1_province", "t3.1.0.1_1_city", "t3.1.0.1_1_county"), sep = "#", remove = FALSE)%>%
  relocate(t3.1.0.1_1_province:t3.1.0.1_1_county, .after = t3.1.0.1_1)%>%
  separate(t3.1.0.5_1, into = c("t3.1.0.5_1_province", "t3.1.0.5_1_city", "t3.1.0.5_1_county"), sep = "#", remove = FALSE)%>%
  relocate(t3.1.0.5_1_province:t3.1.0.5_1_county, .after = t3.1.0.5_1)

 




export( list('第二期已接受治疗' = dat2.1.1,
             '第二期未接受治疗' = dat1.1.1,
             '第一期' = dat3.1.1 ) ,'经济负担问卷数据库-终版.xlsx'  )






