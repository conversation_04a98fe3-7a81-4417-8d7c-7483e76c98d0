import pandas as pd # 数据处理库，用于Excel文件读取和数据分析
import numpy as np  # 数值计算库，用于数学运算
import matplotlib.pyplot as plt   # 绘图库，用于数据可视化
import seaborn as sns    # 绘图库，用于数据可视化
from scipy import stats  # 科学计算库，用于统计分析
import warnings          # 警告控制
warnings.filterwarnings('ignore')  # 忽略警告信息，让输出更清洁

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

def load_resource_data():
    """
    导入resourceall_20240109.xlsx文件
    返回: DataFrame对象
    """
    try:
        # 读取Excel文件
        df = pd.read_excel('resourceall_20240109.xlsx')
        print(f"成功导入数据！数据形状: {df.shape}")
        return df
    except FileNotFoundError:
        print("错误: 找不到文件 'resourceall_20240109.xlsx'")
        return None
    except Exception as e:
        print(f"导入数据时发生错误: {e}")
        return None

def prepare_resource_data(df):
    """
    准备儿童肿瘤医疗资源分析数据
    """
    # 创建分析用的数据副本
    analysis_df = df.copy()

    # 计算治疗后支持性照护 (post_treatment)
    analysis_df['post_treatment'] = (
        analysis_df['Follow_up_examination'].fillna(0) +
        analysis_df['convalescence'].fillna(0)
    )

    # 定义分析的资源变量
    resource_vars = {
        'chemotherapy': '化疗机构',
        'radiotherapy': '放疗机构',
        'surgical_treatment': '外科治疗机构',
        'post_treatment': '治疗后支持性照护机构',
        'Pediatric_oncologist_AD': '儿科肿瘤医生',
        'pathologist': '病理工作者',
        '护理人员数量': '注册护士'
    }

    # 计算人口密度指标（每千人）
    for var in resource_vars.keys():
        if var in analysis_df.columns:
            analysis_df[f'{var}_per_million'] = (
                analysis_df[var] / analysis_df['population_total'] * 1000
            )
 
    # 计算地域密度指标（每万平方公里）
    for var in resource_vars.keys():
        if var in analysis_df.columns:
            analysis_df[f'{var}_per_10k_km2'] = (
                analysis_df[var] / analysis_df['Geographical_area'] * 10000
            )

    return analysis_df, resource_vars

def calculate_gini_coefficient(values):

    """
    计算基尼系数
    """
    values = np.array(values)
    values = values[~np.isnan(values)]  # 移除NaN值
    if len(values) == 0:
        return np.nan

    values = np.sort(values)
    n = len(values)
    cumsum = np.cumsum(values)

    return (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n

def calculate_theil_index(values, groups):
    """
    计算Theil指数
    """
    df_temp = pd.DataFrame({'values': values, 'groups': groups})
    df_temp = df_temp.dropna()

    if len(df_temp) == 0:
        return np.nan, np.nan, np.nan

    # 总体均值
    total_mean = df_temp['values'].mean()
    total_n = len(df_temp)

    # 组内和组间Theil指数
    within_theil = 0
    between_theil = 0

    for group in df_temp['groups'].unique():
        group_data = df_temp[df_temp['groups'] == group]['values']
        group_mean = group_data.mean()
        group_n = len(group_data)
        group_weight = group_n / total_n

        # 组内Theil
        if group_mean > 0:
            group_theil = np.sum(group_data / group_mean * np.log(group_data / group_mean)) / group_n
            within_theil += group_weight * group_theil

        # 组间Theil
        if total_mean > 0 and group_mean > 0:
            between_theil += group_weight * np.log(group_mean / total_mean)

    total_theil = within_theil + between_theil

    return total_theil, within_theil, between_theil

def descriptive_analysis(df, resource_vars):
    """
    2.2.1 描述各省份的儿童肿瘤诊疗设备、医护数量、密度分布
    """
    print("="*80)
    print("2.2.1 各省份儿童肿瘤诊疗资源描述性分析")
    print("="*80)

    # 基本统计描述
    print("\n【资源总量统计】")
    resource_summary = pd.DataFrame()

    for var, name in resource_vars.items():
        if var in df.columns:
            stats_data = {
                '资源类型': name,
                '总量': df[var].sum(),
                '均值': df[var].mean(),
                '中位数': df[var].median(),
                '标准差': df[var].std(),
                '最小值': df[var].min(),
                '最大值': df[var].max(),
                '变异系数': df[var].std() / df[var].mean() if df[var].mean() > 0 else np.nan
            }
            resource_summary = pd.concat([resource_summary, pd.DataFrame([stats_data])], ignore_index=True)

    print(resource_summary.round(2))

    # 人口密度分析
    print("\n【每百万人口资源密度】")
    density_summary = pd.DataFrame()

    for var, name in resource_vars.items():
        density_var = f'{var}_per_million'
        if density_var in df.columns:
            stats_data = {
                '资源类型': name,
                '全国平均': df[density_var].mean(),
                '中位数': df[density_var].median(),
                '标准差': df[density_var].std(),
                '最小值': df[density_var].min(),
                '最大值': df[density_var].max(),
                '变异系数': df[density_var].std() / df[density_var].mean() if df[density_var].mean() > 0 else np.nan
            }
            density_summary = pd.concat([density_summary, pd.DataFrame([stats_data])], ignore_index=True)

    print(density_summary.round(2))

    # 地域密度分析
    print("\n【每万平方公里资源密度】")
    geo_density_summary = pd.DataFrame()

    for var, name in resource_vars.items():
        geo_density_var = f'{var}_per_10k_km2'
        if geo_density_var in df.columns:
            stats_data = {
                '资源类型': name,
                '全国平均': df[geo_density_var].mean(),
                '中位数': df[geo_density_var].median(),
                '标准差': df[geo_density_var].std(),
                '最小值': df[geo_density_var].min(),
                '最大值': df[geo_density_var].max(),
                '变异系数': df[geo_density_var].std() / df[geo_density_var].mean() if df[geo_density_var].mean() > 0 else np.nan
            }
            geo_density_summary = pd.concat([geo_density_summary, pd.DataFrame([stats_data])], ignore_index=True)

    print(geo_density_summary.round(2))

     # 分区域分析 - 修改为使用总量计算密度
    print("\n【分区域资源分布】")
    # 先计算每个分区的总资源和总人口/面积
    regional_totals = df.groupby('分区').agg({
        'chemotherapy': 'sum',
        'radiotherapy': 'sum',
        'surgical_treatment': 'sum',
        'post_treatment': 'sum',
        'Pediatric_oncologist_AD': 'sum',
        'pathologist': 'sum',
        '护理人员数量': 'sum',
        'population_total': 'sum',
        'Geographical_area': 'sum'
    })
    
    # 计算每个分区的人口密度和地理密度
    regional_analysis = pd.DataFrame(index=regional_totals.index)
    
    # 添加资源总量
    for var, name in resource_vars.items():
        if var in regional_totals.columns:
            regional_analysis[f'{name}_总量'] = regional_totals[var]
    
    # 计算人口密度（每百万人口）
    for var, name in resource_vars.items():
        if var in regional_totals.columns:
            regional_analysis[f'{name}_每百万人口'] = (
                regional_totals[var] / regional_totals['population_total'] * 1000000
            )
    
    # 计算地理密度（每万平方公里）
    for var, name in resource_vars.items():
        if var in regional_totals.columns:
            regional_analysis[f'{name}_每万平方公里'] = (
                regional_totals[var] / regional_totals['Geographical_area'] * 10000
            )
    
    # 添加人口和面积信息
    regional_analysis['人口总量'] = regional_totals['population_total']
    regional_analysis['面积总量'] = regional_totals['Geographical_area']
    
    print(regional_analysis.round(2))

    return resource_summary, density_summary, geo_density_summary, regional_analysis

def plot_lorenz_curve(values, title, filename=None):
    """
    绘制洛伦兹曲线并标注基尼系数
    
    参数:
    values -- 资源密度值数组
    title -- 图表标题
    filename -- 如果提供，将图表保存到文件
    """
    # 确保数据有效
    values = np.array(values)
    values = values[~np.isnan(values)]  # 移除NaN值
    if len(values) == 0:
        print(f"警告: 无法绘制{title}的洛伦兹曲线，数据为空")
        return np.nan
    
    # 计算基尼系数
    gini = calculate_gini_coefficient(values)
    
    # 创建图表
    plt.figure(figsize=(10, 6))
    
    # 排序数据
    sorted_values = np.sort(values)
    cumsum = np.cumsum(sorted_values)
    
    # 计算洛伦兹曲线点
    lorenz_points = cumsum / cumsum[-1]
    
    # 绘制洛伦兹曲线
    plt.plot([0, 1], [0, 1], 'r--', label='完全平等线')
    plt.plot(np.linspace(0, 1, len(lorenz_points)), lorenz_points, 'b-', label='洛伦兹曲线')
    
    # 计算洛伦兹曲线与完全平等线之间的面积（基尼系数的可视化）
    plt.fill_between(np.linspace(0, 1, len(lorenz_points)), np.linspace(0, 1, len(lorenz_points)), 
                     lorenz_points, color='skyblue', alpha=0.5, label='不平等面积')
    
    # 添加标题和标签
    plt.title(f"{title} - 洛伦兹曲线 (基尼系数: {gini:.3f})")
    plt.xlabel('人口比例（累计）')
    plt.ylabel('资源比例（累计）')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    # 在图表上标注基尼系数
    plt.annotate(f'基尼系数 = {gini:.3f}', xy=(0.7, 0.2), xycoords='axes fraction', 
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    # 保存图表或显示
    if filename:
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"已保存洛伦兹曲线图表到: {filename}")
    else:
        plt.show()
    
    plt.close()

def create_combined_lorenz_panel(lorenz_results, output_dir="洛伦兹曲线图"):
    """
    将已生成的洛伦兹曲线图按密度类型分别组合成面板图
    
    参数:
    lorenz_results -- 洛伦兹曲线结果DataFrame，包含图表文件路径
    output_dir -- 输出目录
    
    返回:
    filepaths -- 保存的面板图文件路径字典
    """
    import os
    import matplotlib.pyplot as plt
    from matplotlib.gridspec import GridSpec
    from matplotlib.image import imread
    
    # 确保有结果可用
    if lorenz_results is None or len(lorenz_results) == 0:
        print("没有洛伦兹曲线结果可用于创建面板图")
        return None
    
    # 按密度类型分组
    pop_density_files = []
    geo_density_files = []
    
    for _, row in lorenz_results.iterrows():
        if os.path.exists(row['图表文件']):
            if row['密度类型'] == '人口密度':
                pop_density_files.append((row['资源类型'], row['图表文件']))
            elif row['密度类型'] == '地理密度':
                geo_density_files.append((row['资源类型'], row['图表文件']))
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
        except Exception as e:
            print(f"创建目录失败: {e}")
    
    filepaths = {}
    
    # 创建人口密度面板图
    if pop_density_files:
        pop_filepath = create_density_panel(pop_density_files, "人口密度洛伦兹曲线面板图.png", "人口密度分布洛伦兹曲线", output_dir)
        if pop_filepath:
            filepaths['人口密度'] = pop_filepath
    
    # 创建地理密度面板图
    if geo_density_files:
        geo_filepath = create_density_panel(geo_density_files, "地理密度洛伦兹曲线面板图.png", "地理密度分布洛伦兹曲线", output_dir)
        if geo_filepath:
            filepaths['地理密度'] = geo_filepath
    
    return filepaths

def create_density_panel(files, filename, title, output_dir):
    """
    创建特定密度类型的洛伦兹曲线面板图
    
    参数:
    files -- 图表文件列表，每项为(资源类型, 文件路径)
    filename -- 输出文件名
    title -- 面板图标题
    output_dir -- 输出目录
    
    返回:
    filepath -- 保存的面板图文件路径
    """
    import os
    import matplotlib.pyplot as plt
    from matplotlib.gridspec import GridSpec
    from matplotlib.image import imread
    
    if not files:
        return None
    
    # 确定面板布局
    n_files = len(files)
    n_cols = min(3, n_files)  # 最多3列
    n_rows = (n_files + n_cols - 1) // n_cols  # 向上取整计算行数
    
    # 创建图形
    fig = plt.figure(figsize=(n_cols * 6, n_rows * 5))
    gs = GridSpec(n_rows, n_cols, figure=fig)
    
    # 设置总标题
    fig.suptitle(title, fontsize=20)
    
    # 添加每个图表到面板
    for i, (resource, img_path) in enumerate(files):
        row = i // n_cols
        col = i % n_cols
        
        ax = fig.add_subplot(gs[row, col])
        img = imread(img_path)
        ax.imshow(img)
        ax.set_title(f"{resource}", fontsize=14)
        ax.axis('off')  # 隐藏坐标轴
    
    # 调整布局
    plt.tight_layout(rect=[0, 0, 1, 0.97])  # 为顶部标题留出空间
    
    # 保存面板图
    filepath = os.path.join(output_dir, filename)
    try:
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"已保存洛伦兹曲线面板图到: {filepath}")
    except Exception as e:
        print(f"保存面板图失败: {e}")
        filepath = None
    
    plt.close(fig)
    
    return filepath

def equity_analysis(df, resource_vars):
    """
    2.2.2 评价各省份的儿童肿瘤诊疗资源的公平性
    """
    print("\n" + "="*80)
    print("2.2.2 儿童肿瘤诊疗资源公平性评价")
    print("="*80)

    equity_results = pd.DataFrame()
    lorenz_results = []  # 存储洛伦兹曲线结果

    # 创建洛伦兹曲线图输出目录
    import os
    output_dir = "洛伦兹曲线图"
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"已创建目录: {output_dir}")
        except Exception as e:
            print(f"创建目录失败: {e}")

    # 计算各资源的公平性指标
    for var, name in resource_vars.items():
        if var in df.columns:
            # 人口公平性（基于人口密度）
            density_var = f'{var}_per_million'
            if density_var in df.columns:
                pop_gini = calculate_gini_coefficient(df[density_var].values)
                pop_theil_total, pop_theil_within, pop_theil_between = calculate_theil_index(
                    df[density_var].values, df['分区'].values
                )
                
                # 绘制洛伦兹曲线
                lorenz_filename = f"{name}_人口密度分布.png"
                filepath = os.path.join(output_dir, lorenz_filename)
                plot_gini = plot_lorenz_curve(df[density_var].values, 
                                 f"{name}人口密度分布", 
                                 filename=filepath)
                
                # 记录洛伦兹曲线结果
                lorenz_results.append({
                    '资源类型': name,
                    '密度类型': '人口密度',
                    '基尼系数': pop_gini,
                    '图表文件': filepath
                })
            else:
                pop_gini = pop_theil_total = pop_theil_within = pop_theil_between = np.nan

            # 地理公平性（基于地域密度）
            geo_density_var = f'{var}_per_10k_km2'
            if geo_density_var in df.columns:
                geo_gini = calculate_gini_coefficient(df[geo_density_var].values)
                geo_theil_total, geo_theil_within, geo_theil_between = calculate_theil_index(
                    df[geo_density_var].values, df['分区'].values
                )
                
                # 绘制洛伦兹曲线
                lorenz_filename = f"{name}_地理密度分布.png"
                filepath = os.path.join(output_dir, lorenz_filename)
                plot_gini = plot_lorenz_curve(df[geo_density_var].values, 
                                 f"{name}地理密度分布", 
                                 filename=filepath)
                
                # 记录洛伦兹曲线结果
                lorenz_results.append({
                    '资源类型': name,
                    '密度类型': '地理密度',
                    '基尼系数': geo_gini,
                    '图表文件': filepath
                })
            else:
                geo_gini = geo_theil_total = geo_theil_within = geo_theil_between = np.nan

            equity_data = {
                '资源类型': name,
                '人口公平性_基尼系数': pop_gini,
                '人口公平性_Theil总指数': pop_theil_total,
                '人口公平性_Theil组内': pop_theil_within,
                '人口公平性_Theil组间': pop_theil_between,
                '地理公平性_基尼系数': geo_gini,
                '地理公平性_Theil总指数': geo_theil_total,
                '地理公平性_Theil组内': geo_theil_within,
                '地理公平性_Theil组间': geo_theil_between
            }

            equity_results = pd.concat([equity_results, pd.DataFrame([equity_data])], ignore_index=True)


    print("\n【公平性评价结果】")
    print("注：基尼系数和Theil指数越接近0，表明公平性越好")
    print(equity_results.round(4))

    # 公平性解释
    print("\n【公平性评价解释】")
    for idx, row in equity_results.iterrows():
        resource_name = row['资源类型']
        pop_gini = row['人口公平性_基尼系数']
        geo_gini = row['地理公平性_基尼系数']

        print(f"\n{resource_name}:")

        # 人口公平性评价
        if not np.isnan(pop_gini):
            if pop_gini < 0.2:
                pop_equity = "非常公平"
            elif pop_gini < 0.3:
                pop_equity = "相对公平"
            elif pop_gini < 0.4:
                pop_equity = "中等不公平"
            elif pop_gini < 0.5:
                pop_equity = "较不公平"
            else:
                pop_equity = "非常不公平"
            print(f"  人口公平性: {pop_equity} (基尼系数={pop_gini:.3f})")

        # 地理公平性评价
        if not np.isnan(geo_gini):
            if geo_gini < 0.2:
                geo_equity = "非常公平"
            elif geo_gini < 0.3:
                geo_equity = "相对公平"
            elif geo_gini < 0.4:
                geo_equity = "中等不公平"
            elif geo_gini < 0.5:
                geo_equity = "较不公平"
            else:
                geo_equity = "非常不公平"
            print(f"  地理公平性: {geo_equity} (基尼系数={geo_gini:.3f})")

    # 将洛伦兹曲线结果转换为DataFrame
    lorenz_df = pd.DataFrame(lorenz_results)
    
    return equity_results, lorenz_df


    """
    2.2.2 评价各省份的儿童肿瘤诊疗资源的公平性
    """
    print("\n" + "="*80)
    print("2.2.2 儿童肿瘤诊疗资源公平性评价")
    print("="*80)

    equity_results = pd.DataFrame()

    # 计算各资源的公平性指标
    for var, name in resource_vars.items():
        if var in df.columns:
            # 人口公平性（基于人口密度）
            density_var = f'{var}_per_million'
            if density_var in df.columns:
                pop_gini = calculate_gini_coefficient(df[density_var].values)
                pop_theil_total, pop_theil_within, pop_theil_between = calculate_theil_index(
                    df[density_var].values, df['分区'].values
                )
                
                # 绘制洛伦兹曲线
                plot_lorenz_curve(df[density_var].values, 
                                 f"{name}人口密度分布", 
                                 filename=f"洛伦兹曲线_{name}_人口密度.png")
            else:
                pop_gini = pop_theil_total = pop_theil_within = pop_theil_between = np.nan

            # 地理公平性（基于地域密度）
            geo_density_var = f'{var}_per_10k_km2'
            if geo_density_var in df.columns:
                geo_gini = calculate_gini_coefficient(df[geo_density_var].values)
                geo_theil_total, geo_theil_within, geo_theil_between = calculate_theil_index(
                    df[geo_density_var].values, df['分区'].values
                )
                
                # 绘制洛伦兹曲线
                plot_lorenz_curve(df[geo_density_var].values, 
                                 f"{name}地理密度分布", 
                                 filename=f"洛伦兹曲线_{name}_地理密度.png")
            else:
                geo_gini = geo_theil_total = geo_theil_within = geo_theil_between = np.nan

            equity_data = {
                '资源类型': name,
                '人口公平性_基尼系数': pop_gini,
                '人口公平性_Theil总指数': pop_theil_total,
                '人口公平性_Theil组内': pop_theil_within,
                '人口公平性_Theil组间': pop_theil_between,
                '地理公平性_基尼系数': geo_gini,
                '地理公平性_Theil总指数': geo_theil_total,
                '地理公平性_Theil组内': geo_theil_within,
                '地理公平性_Theil组间': geo_theil_between
            }

            equity_results = pd.concat([equity_results, pd.DataFrame([equity_data])], ignore_index=True)

    print("\n【公平性评价结果】")
    print("注：基尼系数和Theil指数越接近0，表明公平性越好")
    print(equity_results.round(4))

    # 公平性解释
    print("\n【公平性评价解释】")
    for idx, row in equity_results.iterrows():
        resource_name = row['资源类型']
        pop_gini = row['人口公平性_基尼系数']
        geo_gini = row['地理公平性_基尼系数']

        print(f"\n{resource_name}:")

        # 人口公平性评价
        if not np.isnan(pop_gini):
            if pop_gini < 0.2:
                pop_equity = "非常公平"
            elif pop_gini < 0.3:
                pop_equity = "相对公平"
            elif pop_gini < 0.4:
                pop_equity = "中等不公平"
            elif pop_gini < 0.5:
                pop_equity = "较不公平"
            else:
                pop_equity = "非常不公平"
            print(f"  人口公平性: {pop_equity} (基尼系数={pop_gini:.3f})")

        # 地理公平性评价
        if not np.isnan(geo_gini):
            if geo_gini < 0.2:
                geo_equity = "非常公平"
            elif geo_gini < 0.3:
                geo_equity = "相对公平"
            elif geo_gini < 0.4:
                geo_equity = "中等不公平"
            elif geo_gini < 0.5:
                geo_equity = "较不公平"
            else:
                geo_equity = "非常不公平"
            print(f"  地理公平性: {geo_equity} (基尼系数={geo_gini:.3f})")

    return equity_results

def accessibility_analysis(df, resource_vars):
    """
    可及性分析：比较东中西部、不同社会经济发展水平下的资源分布
    """
    print("\n" + "="*80)
    print("2.3.4 儿童肿瘤医疗资源可及性分析")
    print("="*80)

    # 按地区分析
    print("\n【按地理区域分析】")
    regional_accessibility = df.groupby('分区').agg({
        'chemotherapy_per_million': 'mean',
        'radiotherapy_per_million': 'mean',
        'surgical_treatment_per_million': 'mean',
        'post_treatment_per_million': 'mean',
        'Pediatric_oncologist_AD_per_million': 'mean',
        'pathologist_per_million': 'mean',
        '护理人员数量_per_million': 'mean',
        'SDI_2017': 'mean',
        'HDI_2020': 'mean',
        'GNI_2020': 'mean'
    }).round(2)

    print(regional_accessibility)

    # 按SDI水平分析
    print("\n【按社会发展指数(SDI)水平分析】")
    # 将SDI分为三个等级
    df['SDI_level'] = pd.cut(df['SDI_2017'],
                            bins=[0, 0.6, 0.7, 1.0],
                            labels=['低SDI', '中SDI', '高SDI'])

    sdi_accessibility = df.groupby('SDI_level').agg({
        'chemotherapy_per_million': 'mean',
        'radiotherapy_per_million': 'mean',
        'surgical_treatment_per_million': 'mean',
        'post_treatment_per_million': 'mean',
        'Pediatric_oncologist_AD_per_million': 'mean',
        'pathologist_per_million': 'mean',
        '护理人员数量_per_million': 'mean',
        'province': 'count'
    }).round(2)

    sdi_accessibility.rename(columns={'province': '省份数量'}, inplace=True)
    print(sdi_accessibility)

    # 资源配置不平衡分析
    print("\n【资源配置不平衡程度分析】")
    imbalance_analysis = pd.DataFrame()

    for var, name in resource_vars.items():
        density_var = f'{var}_per_million'
        if density_var in df.columns:
            max_val = df[density_var].max()
            min_val = df[density_var].min()
            ratio = max_val / min_val if min_val > 0 else np.inf

            max_province = df.loc[df[density_var].idxmax(), 'province']
            min_province = df.loc[df[density_var].idxmin(), 'province']

            imbalance_data = {
                '资源类型': name,
                '最高省份': max_province,
                '最高值': max_val,
                '最低省份': min_province,
                '最低值': min_val,
                '最高/最低比值': ratio
            }

            imbalance_analysis = pd.concat([imbalance_analysis, pd.DataFrame([imbalance_data])], ignore_index=True)

    print(imbalance_analysis.round(2))

    return regional_accessibility, sdi_accessibility, imbalance_analysis

def generate_summary_report(df, resource_vars, resource_summary, equity_results, imbalance_analysis):
    """
    生成总结报告
    """
    print("\n" + "="*80)
    print("儿童肿瘤医疗资源分析总结报告")
    print("="*80)

    print("\n【主要发现】")

    # 1. 资源总量
    print("\n1. 资源总量现状:")
    total_institutions = (df['chemotherapy'].sum() + df['radiotherapy'].sum() +
                         df['surgical_treatment'].sum()) / 3  # 平均机构数
    total_personnel = df['Pediatric_oncologist_AD'].sum() + df['pathologist'].sum()

    print(f"   - 全国儿童肿瘤治疗机构平均约 {total_institutions:.0f} 家")
    print(f"   - 儿科肿瘤医生和病理工作者共 {total_personnel:.0f} 人")
    print(f"   - 注册护士 {df['护理人员数量'].sum():.0f} 人")

    # 2. 公平性评价
    print("\n2. 公平性评价:")
    avg_pop_gini = equity_results['人口公平性_基尼系数'].mean()
    avg_geo_gini = equity_results['地理公平性_基尼系数'].mean()

    print(f"   - 人口公平性平均基尼系数: {avg_pop_gini:.3f}")
    print(f"   - 地理公平性平均基尼系数: {avg_geo_gini:.3f}")

    if avg_pop_gini > 0.4:
        print("   - 人口公平性总体较差，存在明显地区差异")
    elif avg_pop_gini > 0.3:
        print("   - 人口公平性中等，存在一定地区差异")
    else:
        print("   - 人口公平性相对较好")

    # 3. 最大差异
    print("\n3. 地区差异:")
    max_ratio_resource = imbalance_analysis.loc[imbalance_analysis['最高/最低比值'].idxmax()]
    print(f"   - 差异最大的资源: {max_ratio_resource['资源类型']}")
    print(f"   - 最高地区: {max_ratio_resource['最高省份']} ({max_ratio_resource['最高值']:.2f})")
    print(f"   - 最低地区: {max_ratio_resource['最低省份']} ({max_ratio_resource['最低值']:.2f})")
    print(f"   - 差异倍数: {max_ratio_resource['最高/最低比值']:.1f}倍")

    # 4. 政策建议
    print("\n【政策建议】")
    print("1. 加强资源薄弱地区的儿童肿瘤医疗服务能力建设")
    print("2. 建立区域协作机制，促进优质资源共享")
    print("3. 重点支持中西部地区儿童肿瘤专科人才培养")
    print("4. 完善儿童肿瘤分级诊疗体系")
    print("5. 建立儿童肿瘤医疗资源配置标准和评价体系")

   
def export_results_to_excel(results, filename="儿童肿瘤医疗资源分析结果.xlsx"):
    """
    将分析结果导出到Excel文件
    
    参数:
    results -- 包含各种分析结果的字典
    filename -- 输出的Excel文件名
    """
    try:
        print(f"\n正在导出结果到Excel文件: {filename}")
        
        # 检查文件是否已存在且无法访问
        import os
        if os.path.exists(filename):
            try:
                # 尝试打开文件以检查是否可写
                with open(filename, 'a') as f:
                    pass
            except PermissionError:
                # 如果文件被占用，使用新文件名
                base_name, ext = os.path.splitext(filename)
                import datetime
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{base_name}_{timestamp}{ext}"
                print(f"原文件被占用，将使用新文件名: {filename}")
        
        # 使用ExcelWriter创建一个Excel文件，可以包含多个工作表
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 导出资源总量统计
            if 'resource_summary' in results and results['resource_summary'] is not None:
                results['resource_summary'].to_excel(writer, sheet_name='资源总量统计', index=False)
            
            # 导出公平性评价结果
            if 'equity_results' in results and results['equity_results'] is not None:
                results['equity_results'].to_excel(writer, sheet_name='公平性评价', index=False)
            
            # 导出洛伦兹曲线结果
            if 'lorenz_results' in results and results['lorenz_results'] is not None:
                results['lorenz_results'].to_excel(writer, sheet_name='洛伦兹曲线结果', index=False)
            
            # 导出资源不平衡分析
            if 'imbalance_analysis' in results and results['imbalance_analysis'] is not None:
                results['imbalance_analysis'].to_excel(writer, sheet_name='资源不平衡分析', index=False)
            
            # 导出人口密度分析
            if 'density_summary' in results and results['density_summary'] is not None:
                results['density_summary'].to_excel(writer, sheet_name='人口密度分析', index=False)
            
            # 导出地域密度分析
            if 'geo_density_summary' in results and results['geo_density_summary'] is not None:
                results['geo_density_summary'].to_excel(writer, sheet_name='地域密度分析', index=False)
            
            # 导出区域分析 - 处理MultiIndex列
            if 'regional_analysis' in results and results['regional_analysis'] is not None:
                df_temp = results['regional_analysis'].copy()
                
                # 如果有MultiIndex列，将其转换为单级列
                if isinstance(df_temp.columns, pd.MultiIndex):
                    # 将MultiIndex列转换为单级列，使用'_'连接多级名称
                    df_temp.columns = [f"{col[0]}_{col[1]}" if isinstance(col, tuple) else col 
                                      for col in df_temp.columns]
                
                # 确保索引成为数据的一部分
                if not isinstance(df_temp.index, pd.RangeIndex):
                    df_temp = df_temp.reset_index()
                
                df_temp.to_excel(writer, sheet_name='区域分析', index=False)
            
            # 导出区域可及性分析 - 处理MultiIndex列
            if 'regional_accessibility' in results and results['regional_accessibility'] is not None:
                df_temp = results['regional_accessibility'].copy()
                
                # 如果有MultiIndex列，将其转换为单级列
                if isinstance(df_temp.columns, pd.MultiIndex):
                    # 将MultiIndex列转换为单级列，使用'_'连接多级名称
                    df_temp.columns = [f"{col[0]}_{col[1]}" if isinstance(col, tuple) else col 
                                      for col in df_temp.columns]
                
                # 确保索引成为数据的一部分
                if not isinstance(df_temp.index, pd.RangeIndex):
                    df_temp = df_temp.reset_index()
                
                df_temp.to_excel(writer, sheet_name='区域可及性', index=False)
            
            # 导出SDI可及性分析 - 处理MultiIndex列
            if 'sdi_accessibility' in results and results['sdi_accessibility'] is not None:
                df_temp = results['sdi_accessibility'].copy()
                
                # 如果有MultiIndex列，将其转换为单级列
                if isinstance(df_temp.columns, pd.MultiIndex):
                    # 将MultiIndex列转换为单级列，使用'_'连接多级名称
                    df_temp.columns = [f"{col[0]}_{col[1]}" if isinstance(col, tuple) else col 
                                      for col in df_temp.columns]
                
                # 确保索引成为数据的一部分
                if not isinstance(df_temp.index, pd.RangeIndex):
                    df_temp = df_temp.reset_index()
                
                df_temp.to_excel(writer, sheet_name='SDI可及性', index=False)
            
            # 导出原始数据的一部分关键列
            if 'data' in results and results['data'] is not None:
                key_columns = ['province', '分区', 'population_total', 'Geographical_area', 
                              'chemotherapy', 'radiotherapy', 'surgical_treatment', 
                              'Pediatric_oncologist_AD', 'pathologist', '护理人员数量',
                              'SDI_2017', 'HDI_2020']
                
                # 确保所有列都存在
                available_columns = [col for col in key_columns if col in results['data'].columns]
                if available_columns:
                    results['data'][available_columns].to_excel(writer, sheet_name='原始数据', index=False)
            
        print(f"结果已成功导出到: {filename}")
        return True
    
    except Exception as e:
        print(f"导出结果时发生错误: {e}")
        
        # 尝试使用备用文件名
        try:
            import datetime
            import os
            backup_filename = f"儿童肿瘤医疗资源分析结果_备份_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            print(f"尝试使用备用文件名: {backup_filename}")
            
            with pd.ExcelWriter(backup_filename, engine='openpyxl') as writer:
                # 导出资源总量统计
                if 'resource_summary' in results and results['resource_summary'] is not None:
                    results['resource_summary'].to_excel(writer, sheet_name='资源总量统计', index=False)
                
                # 导出其他关键结果
                if 'equity_results' in results and results['equity_results'] is not None:
                    results['equity_results'].to_excel(writer, sheet_name='公平性评价', index=False)
                
                if 'regional_analysis' in results and results['regional_analysis'] is not None:
                    df_temp = results['regional_analysis'].copy()
                    if isinstance(df_temp.index, pd.MultiIndex) or not isinstance(df_temp.index, pd.RangeIndex):
                        df_temp = df_temp.reset_index()
                    df_temp.to_excel(writer, sheet_name='区域分析', index=False)
            
            print(f"结果已成功导出到备用文件: {backup_filename}")
            return True
        
        except Exception as backup_error:
            print(f"备用导出也失败: {backup_error}")
            print("\n请尝试以下解决方案:")
            print("1. 关闭所有可能打开Excel文件的程序")
            print("2. 检查文件是否为只读状态")
            print("3. 尝试使用不同的文件名")
            print("4. 确保您有写入当前目录的权限")
            return False   
def main():
    """
    主函数：执行完整的儿童肿瘤医疗资源分析
    """
    print("开始儿童肿瘤医疗资源分析...")

    # 1. 数据加载
    df = load_resource_data()
    if df is None:
        return None

    # 2. 数据准备
    analysis_df, resource_vars = prepare_resource_data(df)

    # 3. 描述性分析
    resource_summary, density_summary, geo_density_summary, regional_analysis = descriptive_analysis(analysis_df, resource_vars)

    # 4. 公平性评价
    equity_results, lorenz_results = equity_analysis(analysis_df, resource_vars)

    # 创建洛伦兹曲线面板图
    try:
        print("\n正在创建洛伦兹曲线面板图...")
        panel_filepaths = create_combined_lorenz_panel(lorenz_results)
        if panel_filepaths:
            for density_type, filepath in panel_filepaths.items():
                print(f"洛伦兹曲线面板图（{density_type}）已保存到: {filepath}")
            
            # 尝试直接打开图片文件
            try:
                import platform
                import os
                system = platform.system()
                if system == 'Darwin':  # macOS
                    import subprocess
                    subprocess.call(('open', filepath))
                elif system == 'Windows':
                    os.startfile(filepath)
                elif system == 'Linux':
                    import subprocess
                    subprocess.call(('xdg-open', filepath))
                print(f"已尝试自动打开面板图文件")
            except Exception as e:
                print(f"自动打开图片失败: {e}")
    except Exception as e:
        print(f"创建洛伦兹曲线面板图失败: {e}")
        panel_filepaths = None
    # 5. 可及性分析
    regional_accessibility, sdi_accessibility, imbalance_analysis = accessibility_analysis(analysis_df, resource_vars)

    # 6. 总结报告
    generate_summary_report(analysis_df, resource_vars, resource_summary, equity_results, imbalance_analysis)

    # 创建完整的结果字典
    results = {
        'data': analysis_df,
        'resource_summary': resource_summary,
        'equity_results': equity_results,
        'lorenz_results': lorenz_results,
        'imbalance_analysis': imbalance_analysis,
        'density_summary': density_summary,
        'geo_density_summary': geo_density_summary,
        'regional_analysis': regional_analysis,
        'regional_accessibility': regional_accessibility,
        'sdi_accessibility': sdi_accessibility
    }

    # 7. 导出结果到Excel
    export_results_to_excel(results)

    print("\n分析完成！")
    
    return results

if __name__ == "__main__":
    results = main()