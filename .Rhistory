indirect_costs <= 5000 ~ "低间接费用负担",
indirect_costs <= 20000 ~ "中等间接费用负担",
indirect_costs > 20000 ~ "高间接费用负担",
TRUE ~ "其他"
)
print("✅ 费用计算完成")
return(data)
}
# 第四步：应用费用计算
print("\n=== 第四步：应用费用计算 ===")
tryCatch({
dat2.1.1 <- calculate_costs_minimal(dat2.1.1)
print("✅ 费用计算成功应用到数据")
# 显示计算结果摘要
print("\n=== 费用计算结果摘要 ===")
if("total_personal_payment" %in% names(dat2.1.1)) {
cat("平均个人支付：", round(mean(dat2.1.1$total_personal_payment, na.rm = TRUE), 2), "万元\n")
cat("中位数个人支付：", round(median(dat2.1.1$total_personal_payment, na.rm = TRUE), 2), "万元\n")
}
if("economic_burden_intensity" %in% names(dat2.1.1)) {
cat("平均经济负担强度：", round(mean(dat2.1.1$economic_burden_intensity, na.rm = TRUE), 2), "%\n")
}
if("catastrophic_expenditure" %in% names(dat2.1.1)) {
cat("灾难性支出率：", round(mean(dat2.1.1$catastrophic_expenditure, na.rm = TRUE) * 100, 2), "%\n")
}
if("total_social_assistance" %in% names(dat2.1.1)) {
assistance_rate <- mean(dat2.1.1$total_social_assistance > 0, na.rm = TRUE) * 100
cat("社会救助覆盖率：", round(assistance_rate, 2), "%\n")
}
if("total_indirect_costs" %in% names(dat2.1.1)) {
cat("平均间接费用：", round(mean(dat2.1.1$total_indirect_costs, na.rm = TRUE), 0), "元\n")
}
}, error = function(e) {
print(paste("❌ 费用计算失败:", e$message))
print("请检查数据结构或联系技术支持")
})
# 第五步：验证结果
print("\n=== 第五步：验证计算结果 ===")
if("total_personal_payment" %in% names(dat2.1.1)) {
# 检查计算的变量
new_vars <- c("total_personal_payment", "family_annual_income", "economic_burden_intensity",
"catastrophic_expenditure", "total_social_assistance", "total_indirect_costs")
existing_new_vars <- new_vars[new_vars %in% names(dat2.1.1)]
print(paste("成功创建", length(existing_new_vars), "个新变量："))
for(var in existing_new_vars) {
cat("✅", var, "\n")
}
# 显示前几行数据示例
print("\n前3行数据示例：")
if(length(existing_new_vars) > 0) {
print(dat2.1.1[1:3, existing_new_vars])
}
print("\n✅ 费用计算问题已解决！")
print("现在可以继续进行后续分析了。")
} else {
print("❌ 费用计算仍然失败，需要进一步检查")
}
print("\n=== 解决方案完成 ===")
print("如果费用计算成功，您现在可以：")
print("1. 继续应用其他变量转换函数")
print("2. 进行描述性统计分析")
# 根据问卷单位修正经济负担计算
correct_economic_burden_by_units <- function(data) {
print("开始基于问卷单位修正经济负担计算...")
# 检查关键变量
if(!"t3.4" %in% names(data)) {
stop("❌ 个人支付变量t3.4不存在")
}
# 确定收入变量
income_var <- NULL
if("t4.2_2" %in% names(data)) {
income_var <- "t4.2_2"
print("使用t4.2_2（确诊前月均收入，单位：元）")
} else if("t4.3.2" %in% names(data)) {
income_var <- "t4.3.2"
print("使用t4.3.2（确诊后月均收入，单位：元）")
} else {
stop("❌ 收入变量不存在")
}
print("\n=== 问卷单位说明 ===")
print("t3.4 个人支付医疗费用：万元")
print("t4.2_2 确诊前家庭月均收入：元")
print("t4.3.2 确诊后家庭月均收入：元")
print("t3.9-t3.15 间接费用：元")
# 数据清洗和单位统一
data_corrected <- data %>%
mutate(
# 个人支付（万元 → 万元，保持不变）
personal_payment_wan = ifelse(is.na(t3.4) | t3.4 < 0, 0, t3.4),
# 月收入（元 → 万元）
monthly_income_yuan = ifelse(is.na(.data[[income_var]]) | .data[[income_var]] < 0,
NA, .data[[income_var]]),
monthly_income_wan = monthly_income_yuan / 10000,  # 元转万元
# 年收入（万元）
annual_income_wan = monthly_income_wan * 12,
# 重新计算经济负担强度（统一单位：万元）
economic_burden_corrected = case_when(
is.na(annual_income_wan) | annual_income_wan <= 0 ~ NA_real_,
TRUE ~ personal_payment_wan / annual_income_wan * 100
),
# 重新计算灾难性支出
catastrophic_corrected = case_when(
is.na(economic_burden_corrected) ~ FALSE,
economic_burden_corrected > 40 ~ TRUE,
TRUE ~ FALSE
),
# 社会救助（万元）
medical_assistance_wan = ifelse(!is.na(t3.2_1), t3.2_1, 0),
charity_donation_wan = ifelse(!is.na(t3.3_1), t3.3_1, 0),
total_social_assistance_wan = medical_assistance_wan + charity_donation_wan,
# 间接费用（元 → 万元）
transportation_cost_yuan = ifelse(!is.na(t3.9), t3.9, 0),
accommodation_cost_yuan = ifelse(!is.na(t3.10), t3.10, 0),
dining_cost_yuan = ifelse(!is.na(t3.11), t3.11, 0),
nutrition_cost_yuan = ifelse(!is.na(t3.12), t3.12, 0),
nursing_cost_yuan = ifelse(!is.na(t3.13), t3.13, 0),
equipment_cost_yuan = ifelse(!is.na(t3.14), t3.14, 0),
reward_cost_yuan = ifelse(!is.na(t3.15), t3.15, 0),
# 总间接费用（元 → 万元）
total_indirect_costs_yuan = transportation_cost_yuan + accommodation_cost_yuan +
dining_cost_yuan + nutrition_cost_yuan + nursing_cost_yuan +
equipment_cost_yuan + reward_cost_yuan,
total_indirect_costs_wan = total_indirect_costs_yuan / 10000,
# 间接费用占比
indirect_cost_ratio = ifelse(personal_payment_wan > 0,
total_indirect_costs_wan / personal_payment_wan * 100, 0),
# 社会救助缓解比例
social_assistance_ratio = ifelse(personal_payment_wan > 0,
total_social_assistance_wan / personal_payment_wan * 100, 0),
# 经济负担分级
burden_level = case_when(
is.na(economic_burden_corrected) ~ "无收入数据",
economic_burden_corrected == 0 ~ "无经济负担",
economic_burden_corrected <= 10 ~ "轻度负担(≤10%)",
economic_burden_corrected <= 20 ~ "中度负担(10-20%)",
economic_burden_corrected <= 40 ~ "重度负担(20-40%)",
economic_burden_corrected > 40 ~ "灾难性负担(>40%)",
TRUE ~ "其他"
),
# 间接费用负担程度
indirect_burden_level = case_when(
total_indirect_costs_yuan == 0 ~ "无间接费用",
total_indirect_costs_yuan <= 5000 ~ "低间接费用负担(≤5千元)",
total_indirect_costs_yuan <= 20000 ~ "中等间接费用负担(5千-2万元)",
total_indirect_costs_yuan <= 50000 ~ "较高间接费用负担(2-5万元)",
total_indirect_costs_yuan > 50000 ~ "高间接费用负担(>5万元)",
TRUE ~ "其他"
)
)
# 显示修正结果
print("\n=== 修正计算结果 ===")
# 基本统计
valid_burden <- data_corrected$economic_burden_corrected[!is.na(data_corrected$economic_burden_corrected)]
valid_catastrophic <- data_corrected$catastrophic_corrected[!is.na(data_corrected$catastrophic_corrected)]
if(length(valid_burden) > 0) {
print(paste("有效经济负担记录数：", length(valid_burden)))
print(paste("平均经济负担强度：", round(mean(valid_burden), 2), "%"))
print(paste("中位数经济负担强度：", round(median(valid_burden), 2), "%"))
print(paste("灾难性支出率：", round(mean(valid_catastrophic) * 100, 2), "%"))
print("\n经济负担强度分布：")
print(summary(valid_burden))
print("\n经济负担分级分布：")
burden_table <- table(data_corrected$burden_level)
burden_prop <- prop.table(burden_table) * 100
for(i in 1:length(burden_table)) {
cat(names(burden_table)[i], ":", burden_table[i], "例 (", round(burden_prop[i], 1), "%)\n")
}
}
# 间接费用统计
print("\n=== 间接费用统计 ===")
print(paste("平均间接费用：", round(mean(data_corrected$total_indirect_costs_yuan, na.rm = TRUE), 0), "元"))
print(paste("中位数间接费用：", round(median(data_corrected$total_indirect_costs_yuan, na.rm = TRUE), 0), "元"))
print(paste("平均间接费用占个人支付比例：", round(mean(data_corrected$indirect_cost_ratio, na.rm = TRUE), 2), "%"))
print("\n间接费用负担分级分布：")
indirect_table <- table(data_corrected$indirect_burden_level)
indirect_prop <- prop.table(indirect_table) * 100
for(i in 1:length(indirect_table)) {
cat(names(indirect_table)[i], ":", indirect_table[i], "例 (", round(indirect_prop[i], 1), "%)\n")
}
# 社会救助统计
print("\n=== 社会救助统计 ===")
assistance_rate <- mean(data_corrected$total_social_assistance_wan > 0, na.rm = TRUE) * 100
print(paste("社会救助覆盖率：", round(assistance_rate, 2), "%"))
if(assistance_rate > 0) {
assistance_amount <- data_corrected$total_social_assistance_wan[data_corrected$total_social_assistance_wan > 0]
print(paste("平均救助金额：", round(mean(assistance_amount, na.rm = TRUE), 2), "万元"))
print(paste("平均救助缓解比例：", round(mean(data_corrected$social_assistance_ratio[data_corrected$social_assistance_ratio > 0], na.rm = TRUE), 2), "%"))
}
# 数据质量检查
print("\n=== 数据质量检查 ===")
total_records <- nrow(data_corrected)
valid_payment <- sum(data_corrected$personal_payment_wan > 0, na.rm = TRUE)
valid_income <- sum(!is.na(data_corrected$annual_income_wan) & data_corrected$annual_income_wan > 0)
valid_burden_count <- sum(!is.na(data_corrected$economic_burden_corrected))
print(paste("总记录数：", total_records))
print(paste("有效个人支付记录：", valid_payment, "（", round(valid_payment/total_records*100, 1), "%）"))
print(paste("有效收入记录：", valid_income, "（", round(valid_income/total_records*100, 1), "%）"))
print(paste("有效经济负担记录：", valid_burden_count, "（", round(valid_burden_count/total_records*100, 1), "%）"))
return(data_corrected)
}
# 应用修正函数
if(exists("dat2.1.1")) {
print("应用基于问卷单位的修正函数...")
tryCatch({
dat2.1.1_corrected <- correct_economic_burden_by_units(dat2.1.1)
# 更新原数据中的相关变量
dat2.1.1$total_personal_payment <- dat2.1.1_corrected$personal_payment_wan
dat2.1.1$family_monthly_income_before <- dat2.1.1_corrected$monthly_income_wan
dat2.1.1$family_annual_income <- dat2.1.1_corrected$annual_income_wan
dat2.1.1$economic_burden_intensity <- dat2.1.1_corrected$economic_burden_corrected
dat2.1.1$catastrophic_expenditure <- dat2.1.1_corrected$catastrophic_corrected
dat2.1.1$total_social_assistance <- dat2.1.1_corrected$total_social_assistance_wan
dat2.1.1$total_indirect_costs <- dat2.1.1_corrected$total_indirect_costs_yuan
dat2.1.1$burden_level <- dat2.1.1_corrected$burden_level
dat2.1.1$indirect_burden_level <- dat2.1.1_corrected$indirect_burden_level
dat2.1.1$indirect_cost_ratio <- dat2.1.1_corrected$indirect_cost_ratio
dat2.1.1$social_assistance_ratio <- dat2.1.1_corrected$social_assistance_ratio
print("\n✅ 基于问卷单位的修正计算完成")
# 生成最终摘要
print("\n=== 最终修正结果摘要 ===")
final_summary <- dat2.1.1 %>%
summarise(
总样本量 = n(),
有效经济负担记录 = sum(!is.na(economic_burden_intensity)),
平均个人支付_万元 = round(mean(total_personal_payment, na.rm = TRUE), 2),
中位数个人支付_万元 = round(median(total_personal_payment, na.rm = TRUE), 2),
平均月收入_万元 = round(mean(family_monthly_income_before, na.rm = TRUE), 2),
平均年收入_万元 = round(mean(family_annual_income, na.rm = TRUE), 2),
平均经济负担强度_百分比 = round(mean(economic_burden_intensity, na.rm = TRUE), 2),
中位数经济负担强度_百分比 = round(median(economic_burden_intensity, na.rm = TRUE), 2),
灾难性支出率_百分比 = round(mean(catastrophic_expenditure, na.rm = TRUE) * 100, 2),
社会救助覆盖率_百分比 = round(mean(total_social_assistance > 0, na.rm = TRUE) * 100, 2),
平均间接费用_元 = round(mean(total_indirect_costs, na.rm = TRUE), 0)
)
print("修正后统计摘要：")
print(final_summary)
# 保存修正后的数据和结果
if(!dir.exists("单位修正结果")) {
dir.create("单位修正结果")
}
write.csv(dat2.1.1, "单位修正结果/单位修正后完整数据.csv", row.names = FALSE, fileEncoding = "UTF-8")
write.csv(final_summary, "单位修正结果/单位修正后统计摘要.csv", row.names = FALSE, fileEncoding = "UTF-8")
# 保存详细的分级统计
burden_level_stats <- dat2.1.1 %>%
count(burden_level) %>%
mutate(percentage = round(n / sum(n) * 100, 2))
indirect_level_stats <- dat2.1.1 %>%
count(indirect_burden_level) %>%
mutate(percentage = round(n / sum(n) * 100, 2))
write.csv(burden_level_stats, "单位修正结果/经济负担分级统计.csv", row.names = FALSE, fileEncoding = "UTF-8")
write.csv(indirect_level_stats, "单位修正结果/间接费用分级统计.csv", row.names = FALSE, fileEncoding = "UTF-8")
print("✅ 修正后数据和统计结果已保存到'单位修正结果'文件夹")
}, error = function(e) {
print(paste("❌ 修正过程出错:", e$message))
})
} else {
print("❌ dat2.1.1数据不存在")
}
print("\n=== 基于问卷单位的修正完成 ===")
print("现在的计算应该是正确的：")
print("- 个人支付：万元")
print("- 家庭收入：元 → 万元（已转换）")
print("- 间接费用：元")
print("- 经济负担强度 = 个人支付(万元) / 年收入(万元) × 100%")
create_region_groups
# 基于实际治疗时长的计算函数
calculate_actual_time_matched <- function(data) {
print("使用实际治疗时长进行时间匹配计算...")
# 检查关键变量
if(!"t3.4" %in% names(data)) {
stop("缺少个人医疗支出变量 t3.4")
}
if(!"t1.2" %in% names(data)) {
stop("缺少确诊日期变量 t1.2，无法计算实际治疗时长")
}
# 确定收入变量
income_var <- NULL
if("t4.3_2" %in% names(data)) {
income_var <- "t4.3_2"
print("使用确诊后收入 t4.3_2")
} else if("t4.2_2" %in% names(data)) {
income_var <- "t4.2_2"
print("使用确诊前收入 t4.2_2（备选）")
} else {
stop("缺少收入变量")
}
# 计算实际治疗时长
current_date <- Sys.Date()
data %>%
mutate(
# 分子：确诊至今个人医疗支出（万元）
medical_cost_total = ifelse(is.na(t3.4) | t3.4 < 0, 0, t3.4),
# 确诊日期处理
diagnosis_date = as.Date(t1.2),
# 实际治疗时长（天数转月数）
actual_treatment_days = as.numeric(difftime(current_date, diagnosis_date, units = "days")),
actual_treatment_months = actual_treatment_days / 30.44,  # 平均每月30.44天
# 确诊后月收入（元 → 万元）
monthly_income = ifelse(is.na(.data[[income_var]]) | .data[[income_var]] < 0,
NA, .data[[income_var]] / 10000),
# 分母：确诊至今家庭总收入（万元）
total_income_actual_period = case_when(
!is.na(monthly_income) & !is.na(actual_treatment_months) & actual_treatment_months > 0 ~
monthly_income * actual_treatment_months,
TRUE ~ NA_real_
),
# 实际时间匹配的经济负担强度
burden_intensity_actual = case_when(
is.na(total_income_actual_period) | total_income_actual_period <= 0 ~ NA_real_,
TRUE ~ medical_cost_total / total_income_actual_period * 100
),
# 灾难性支出判断（40%阈值）
catastrophic_actual = case_when(
is.na(burden_intensity_actual) ~ FALSE,
burden_intensity_actual > 40 ~ TRUE,
TRUE ~ FALSE
),
# 不同阈值的灾难性支出
catastrophic_40_actual = burden_intensity_actual > 40,
# 负担程度分级
burden_category_actual = case_when(
is.na(burden_intensity_actual) ~ "无法计算",
burden_intensity_actual <= 10 ~ "轻度负担(≤10%)",
burden_intensity_actual <= 25 ~ "中度负担(10-25%)",
burden_intensity_actual <= 40 ~ "重度负担(25-40%)",
burden_intensity_actual > 40 ~ "灾难性负担(>40%)",
TRUE ~ "其他"
),
# 治疗时长分组
treatment_duration_category = case_when(
is.na(actual_treatment_months) ~ "未知",
actual_treatment_months <= 3 ~ "短期(≤3个月)",
actual_treatment_months <= 6 ~ "中短期(3-6个月)",
actual_treatment_months <= 12 ~ "中期(6-12个月)",
actual_treatment_months <= 24 ~ "长期(1-2年)",
actual_treatment_months > 24 ~ "超长期(>2年)",
TRUE ~ "其他"
),
# 月均医疗支出
monthly_medical_cost = case_when(
!is.na(actual_treatment_months) & actual_treatment_months > 0 ~
medical_cost_total / actual_treatment_months,
TRUE ~ NA_real_
),
# 月均医疗支出占月收入比例
monthly_burden_ratio_actual = case_when(
!is.na(monthly_medical_cost) & !is.na(monthly_income) & monthly_income > 0 ~
monthly_medical_cost / monthly_income * 100,
TRUE ~ NA_real_
)
)
}
# 执行计算
print("开始基于实际治疗时长的计算...")
dat2.1.1 <- calculate_actual_time_matched(dat2.1.1)
# 基于实际治疗时长的计算函数
calculate_actual_time_matched <- function(data) {
print("使用实际治疗时长进行时间匹配计算...")
# 检查关键变量
if(!"t3.4" %in% names(data)) {
stop("缺少个人医疗支出变量 t3.4")
}
if(!"t1.2" %in% names(data)) {
stop("缺少确诊日期变量 t1.2，无法计算实际治疗时长")
}
# 确定收入变量
income_var <- NULL
if("t4.3.2" %in% names(data)) {
income_var <- "t4.3.2"
print("使用确诊后收入 t4.3.2")
} else if("t4.2.2" %in% names(data)) {
income_var <- "t4.2.2"
print("使用确诊前收入 t4.2.2（备选）")
} else {
stop("缺少收入变量")
}
# 计算实际治疗时长
current_date <- Sys.Date()
data %>%
mutate(
# 分子：确诊至今个人医疗支出（万元）
medical_cost_total = ifelse(is.na(t3.4) | t3.4 < 0, 0, t3.4),
# 确诊日期处理
diagnosis_date = as.Date(t1.2),
# 实际治疗时长（天数转月数）
actual_treatment_days = as.numeric(difftime(current_date, diagnosis_date, units = "days")),
actual_treatment_months = actual_treatment_days / 30.44,  # 平均每月30.44天
# 确诊后月收入（元 → 万元）
monthly_income = ifelse(is.na(.data[[income_var]]) | .data[[income_var]] < 0,
NA, .data[[income_var]] / 10000),
# 分母：确诊至今家庭总收入（万元）
total_income_actual_period = case_when(
!is.na(monthly_income) & !is.na(actual_treatment_months) & actual_treatment_months > 0 ~
monthly_income * actual_treatment_months,
TRUE ~ NA_real_
),
# 实际时间匹配的经济负担强度
burden_intensity_actual = case_when(
is.na(total_income_actual_period) | total_income_actual_period <= 0 ~ NA_real_,
TRUE ~ medical_cost_total / total_income_actual_period * 100
),
# 灾难性支出判断（40%阈值）
catastrophic_actual = case_when(
is.na(burden_intensity_actual) ~ FALSE,
burden_intensity_actual > 40 ~ TRUE,
TRUE ~ FALSE
),
# 不同阈值的灾难性支出
catastrophic_40_actual = burden_intensity_actual > 40,
# 负担程度分级
burden_category_actual = case_when(
is.na(burden_intensity_actual) ~ "无法计算",
burden_intensity_actual <= 10 ~ "轻度负担(≤10%)",
burden_intensity_actual <= 25 ~ "中度负担(10-25%)",
burden_intensity_actual <= 40 ~ "重度负担(25-40%)",
burden_intensity_actual > 40 ~ "灾难性负担(>40%)",
TRUE ~ "其他"
),
# 治疗时长分组
treatment_duration_category = case_when(
is.na(actual_treatment_months) ~ "未知",
actual_treatment_months <= 3 ~ "短期(≤3个月)",
actual_treatment_months <= 6 ~ "中短期(3-6个月)",
actual_treatment_months <= 12 ~ "中期(6-12个月)",
actual_treatment_months <= 24 ~ "长期(1-2年)",
actual_treatment_months > 24 ~ "超长期(>2年)",
TRUE ~ "其他"
),
# 月均医疗支出
monthly_medical_cost = case_when(
!is.na(actual_treatment_months) & actual_treatment_months > 0 ~
medical_cost_total / actual_treatment_months,
TRUE ~ NA_real_
),
# 月均医疗支出占月收入比例
monthly_burden_ratio_actual = case_when(
!is.na(monthly_medical_cost) & !is.na(monthly_income) & monthly_income > 0 ~
monthly_medical_cost / monthly_income * 100,
TRUE ~ NA_real_
)
)
}
# 执行计算
print("开始基于实际治疗时长的计算...")
dat2.1.1 <- calculate_actual_time_matched(dat2.1.1)
# 显示结果
print("\n=== 基于实际治疗时长的计算结果 ===")
# 基本统计
result_summary <- dat2.1.1 %>%
summarise(
总样本量 = n(),
有效确诊日期记录 = sum(!is.na(diagnosis_date)),
有效计算记录 = sum(!is.na(burden_intensity_actual)),
平均医疗支出_万元 = round(mean(medical_cost_total, na.rm = TRUE), 2),
中位数医疗支出_万元 = round(median(medical_cost_total, na.rm = TRUE), 2),
平均实际治疗时长_月 = round(mean(actual_treatment_months, na.rm = TRUE), 1),
中位数实际治疗时长_月 = round(median(actual_treatment_months, na.rm = TRUE), 1),
最短治疗时长_月 = round(min(actual_treatment_months, na.rm = TRUE), 1),
最长治疗时长_月 = round(max(actual_treatment_months, na.rm = TRUE), 1),
平均期间总收入_万元 = round(mean(total_income_actual_period, na.rm = TRUE), 2),
平均经济负担强度_百分比 = round(mean(burden_intensity_actual, na.rm = TRUE), 2),
中位数经济负担强度_百分比 = round(median(burden_intensity_actual, na.rm = TRUE), 2),
灾难性支出率_10阈值 = round(mean(catastrophic_10_actual, na.rm = TRUE) * 100, 2),
灾难性支出率_25阈值 = round(mean(catastrophic_25_actual, na.rm = TRUE) * 100, 2),
灾难性支出率_40阈值 = round(mean(catastrophic_40_actual, na.rm = TRUE) * 100, 2),
平均月均医疗支出_万元 = round(mean(monthly_medical_cost, na.rm = TRUE), 2)
)
print(result_summary)
# 基本统计
# 基本统计
result_summary <- dat2.1.1 %>%
summarise(
总样本量 = n(),
有效确诊日期记录 = sum(!is.na(diagnosis_date)),
有效计算记录 = sum(!is.na(burden_intensity_actual)),
平均医疗支出_万元 = round(mean(medical_cost_total, na.rm = TRUE), 2),
中位数医疗支出_万元 = round(median(medical_cost_total, na.rm = TRUE), 2),
平均实际治疗时长_月 = round(mean(actual_treatment_months, na.rm = TRUE), 1),
中位数实际治疗时长_月 = round(median(actual_treatment_months, na.rm = TRUE), 1),
最短治疗时长_月 = round(min(actual_treatment_months, na.rm = TRUE), 1),
最长治疗时长_月 = round(max(actual_treatment_months, na.rm = TRUE), 1),
平均期间总收入_万元 = round(mean(total_income_actual_period, na.rm = TRUE), 2),
平均经济负担强度_百分比 = round(mean(burden_intensity_actual, na.rm = TRUE), 2),
中位数经济负担强度_百分比 = round(median(burden_intensity_actual, na.rm = TRUE), 2),
灾难性支出率_40阈值_百分比 = round(mean(catastrophic_40_actual, na.rm = TRUE) * 100, 2),
平均月均医疗支出_万元 = round(mean(monthly_medical_cost, na.rm = TRUE), 2),
平均月均负担比例_百分比 = round(mean(monthly_burden_ratio_actual, na.rm = TRUE), 2)
)
# 以更清晰的格式显示结果
cat("=== 基于实际治疗时长的统计结果 ===\n\n")
cat("样本概况：\n")
cat("  总样本量：", result_summary$总样本量, "例\n")
cat("  有效确诊日期记录：", result_summary$有效确诊日期记录, "例\n")
cat("  有效计算记录：", result_summary$有效计算记录, "例\n\n")
cat("医疗支出：\n")
cat("  平均医疗支出：", result_summary$平均医疗支出_万元, "万元\n")
cat("  中位数医疗支出：", result_summary$中位数医疗支出_万元, "万元\n\n")
cat("治疗时长：\n")
cat("  平均治疗时长：", result_summary$平均实际治疗时长_月, "个月\n")
cat("  中位数治疗时长：", result_summary$中位数实际治疗时长_月, "个月\n")
cat("  治疗时长范围：", result_summary$最短治疗时长_月, "-", result_summary$最长治疗时长_月, "个月\n\n")
cat("期间收入：\n")
cat("  平均期间总收入：", result_summary$平均期间总收入_万元, "万元\n\n")
cat("经济负担强度：\n")
cat("  平均经济负担强度：", result_summary$平均经济负担强度_百分比, "%\n")
cat("  中位数经济负担强度：", result_summary$中位数经济负担强度_百分比, "%\n\n")
cat("灾难性支出：\n")
cat("  灾难性支出率（40%阈值）：", result_summary$灾难性支出率_40阈值_百分比, "%\n\n")
cat("月均指标：\n")
cat("  平均月均医疗支出：", result_summary$平均月均医疗支出_万元, "万元\n")
cat("  平均月均负担比例：", result_summary$平均月均负担比例_百分比, "%\n")
print(result_summary)
